{"name": "fsse-web", "version": "1.0.0", "description": "监测系统", "main": "index.js", "type": "module", "scripts": {"prepare": "husky", "postinstall": "sh ./scripts/submodule-husky.sh", "preinstall": "npx only-allow pnpm", "server": "node ./scripts/server.js", "build": "node ./scripts/build.js", "build:ui": "node ./scripts/build-ui.js", "dev:docs": "vitepress dev docs", "build:docs": "vitepress build docs", "preview:docs": "vitepress preview docs", "lint-staged": "lint-staged --config .lintstagedrc", "lint": "run-s lint:es lint:prettier lint:style", "lint:es": "eslint --cache", "lint:style": "stylelint \"**/*.{css,less,vue}\" --cache  --allow-empty-input", "lint:prettier": "prettier --check \"**/*.{css,less,js,vue}\" --cache", "fix": "run-s fix:es fix:prettier fix:style ", "fix:es": "eslint --cache  --fix", "fix:style": "stylelint \"**/*.{css,less,vue}\" --fix --allow-empty-input  --cache", "fix:prettier": "prettier --write \"**/*.{css,less,js,vue}\" --cache"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@ant-design/icons-vue": "^7.0.1", "@highlightjs/vue-plugin": "^2.1.0", "@tinymce/tinymce-vue": "^6.1.0", "@unocss/preset-rem-to-px": "^0.55.0", "@unocss/reset": "^0.55.0", "@vitejs/plugin-vue-jsx": "^4.0.1", "@vueuse/core": "^13.0.0", "ant-design-vue": "^4.2.5", "axios": "^1.7.7", "codemirror": "^5.65.20", "codemirror-editor-vue3": "^2.8.0", "crypto-js": "^4.1.1", "dayjs": "^1.11.9", "dom-to-image-more": "^3.1.6", "encryptlong": "^3.1.4", "highlight.js": "^11.10.0", "husky": "^9.1.6", "js-base64": "^3.7.5", "jsbarcode": "^3.11.5", "jspdf": "^2.5.1", "less": "^4.2.0", "lodash": "^4.17.21", "monaco-editor": "^0.52.2", "nanoid": "^5.1.5", "pinia": "^2.2.2", "pinia-plugin-persistedstate": "4.0.2", "print-js": "^1.6.0", "sortablejs": "^1.15.3", "sql-formatter": "^15.5.1", "terser": "^5.34.1", "tinymce": "^7.6.0", "turbo": "^2.1.2", "typed.js": "^2.1.0", "ua-parser-js": "^2.0.2", "unocss": "0.60.2", "unplugin-auto-import": "^0.18.3", "unplugin-vue-components": "^0.27.4", "uuid": "^11.0.3", "virtua": "^0.39.3", "vite-multiple-assets": "^2.1.2", "vite-plugin-vue-setup-extend": "^0.4.0", "vitepress": "^1.3.4", "vue": "^3.4.37", "vue-draggable-plus": "^0.6.0", "vue-router": "^4.4.5", "vue-to-print": "^1.4.0", "xgplayer": "^3.0.9", "xgplayer-mp4": "^3.0.9", "yide-ui-vue": "latest"}, "devDependencies": {"@eslint/js": "^9.11.1", "@inquirer/select": "^3.0.1", "@vitejs/plugin-vue": "^5.1.2", "commitizen": "^4.3.0", "cz-conventional-changelog": "^3.3.0", "esbuild": "^0.25.1", "eslint": "^9.11.1", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-vue": "^9.28.0", "globals": "^15.9.0", "lint-staged": "^15.2.10", "npm-run-all": "^4.1.5", "postcss-html": "^1.7.0", "prettier": "^3.3.3", "stylelint": "^16.9.0", "stylelint-config-recommended-less": "^3.0.1", "stylelint-config-recommended-vue": "^1.5.0", "vite": "^5.4.1", "vite-plugin-app-loading": "^0.3.0"}}