<template>
  <div class="database">
    <div class="headerConBox">
      <div class="header_left">
        <a-button @click="goBack">返回</a-button>
        <div class="header_left_title">数据库管理</div>
      </div>
    </div>
    <div p-16>
      <searchForm
        v-model:formState="query"
        :formList="formList"
        @submit="queryBySubmit"
        @reset="queryByReset"
        mb-20
      ></searchForm>
      <div class="btn-wrap" pb16 flex flex-justify-end>
        <a-button type="primary" @click="openModalBase('新增')">新增</a-button>
        <a-button
          danger
          @click="delBase"
          :disabled="!state.selectedRowKeys.length"
          >删除</a-button
        >
      </div>
      <div>
        <ETable
          hash="database_table"
          :loading="page.loading"
          :columns="columns"
          :dataSource="page.list"
          :total="page.total"
          @paginationChange="paginationChange"
          :current="query.pageNo"
          :row-selection="{
            selectedRowKeys: state.selectedRowKeys,
            onChange: onSelectChange,
          }"
        >
          <template #status="{ record }">
            <a-badge
              :color="getStatusObj(record.status).color"
              :text="getStatusObj(record.status).text"
            />
          </template>

          <template #operation="{ record }">
            <a-button
              :disabled="true"
              type="link"
              class="btn-link-color"
              @click="openModalBase('编辑', record)"
              >编辑</a-button
            >
          </template>
        </ETable>
      </div>
    </div>
  </div>
  <setupModal ref="setupModalRef" @refresh="refresh"></setupModal>
</template>

<script setup>
import { createVNode } from 'vue';
import { Modal } from 'ant-design-vue';
import { ExclamationCircleFilled } from '@ant-design/icons-vue';
import setupModal from './setupModal.vue';

const setupModalRef = ref(null);
const router = useRouter();
const state = reactive({
  selectedRowKeys: [],
});

let { query, page, getList, reset, paginationChange } = useList(
  '/admin/datasource/page'
);

// 显示状态
const statusObj = {
  disabled: {
    text: '已禁用',
    color: '#F5222D',
  },
  enabled: {
    text: '已启用',
    color: '#00C088',
  },
  default: {
    text: '-',
    color: '',
  }, // 默认值存储在 default 属性中
};
function getStatusObj(key) {
  return statusObj[key] || statusObj.default;
}

const formList = [
  {
    type: 'input',
    value: 'code',
    label: '数据源编码',
  },
  {
    type: 'input',
    value: 'name',
    label: '数据源名称',
  },
  {
    type: 'select',
    value: 'type',
    label: '数据源类型',
    list: [
      {
        label: 'mysql',
        value: 'mysql',
      },
    ],
  },
];

const columns = [
  { title: '数据源编码', dataIndex: 'code' },
  { title: '数据源名称', dataIndex: 'name' },
  { title: '数据源描述', dataIndex: 'description' },
  { title: '数据源类型', dataIndex: 'type' },
  { title: '状态', dataIndex: 'status' },
  { title: '操作', dataIndex: 'operation', fixed: 'right', width: 100 },
];

const onSelectChange = selectedRowKeys => {
  state.selectedRowKeys = selectedRowKeys;
};

const queryBySubmit = () => {
  getList();
};

const queryByReset = () => {
  reset();
};

const goBack = () => {
  router.push('/dataCentre/dataManage');
};

const openModalBase = title => {
  setupModalRef.value.showModal(title);
};

const delBase = () => {
  Modal.confirm({
    title: '删除',
    icon: createVNode(ExclamationCircleFilled),
    content: `是否确认删除？`,
    okText: '确 定',
    cancelText: '取 消',
    onCancel() {},
    async onOk() {
      const res = await http.post(
        '/admin/datasource/delete',
        state.selectedRowKeys
      );
      YMessage.success(res.message);
      state.selectedRowKeys = [];
      getList();
    },
  });
};

const refresh = () => {
  getList();
};

onMounted(() => {
  getList();
});
</script>

<style lang="less" scoped>
.database {
  height: 100%;
}

.headerConBox {
  height: 57px;
  border-bottom: 1px solid #d9d9d9;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-left: 16px;
  padding-right: 16px;
}
.header_left {
  display: flex;
  align-items: center;
}
.header_left_title {
  padding-left: 16px;
  font-size: 18px;
  font-weight: 600;
}
</style>
