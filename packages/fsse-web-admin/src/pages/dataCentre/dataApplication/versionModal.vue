<template>
  <a-modal
    :body-style="{ padding: '24px' }"
    v-model:open="state.openModal"
    :title="state.title"
    @ok="handleVersion"
    @cancel="handleCancel"
  >
    <a-form :model="formState" layout="vertical" name="basic" ref="formRef">
      <a-form-item
        mb20
        label="版本名称"
        name="name"
        :rules="[{ required: true, message: '请输入!' }]"
      >
        <a-input
          show-count
          :maxlength="20"
          v-model:value="formState.name"
          placeholder="请输入"
        />
      </a-form-item>
      <a-form-item
        mb20
        label="版本编码"
        name="code"
        :rules="[{ required: true, message: '请输入!' }]"
      >
        <a-input
          show-count
          :maxlength="20"
          v-model:value="formState.code"
          placeholder="请输入"
          :disabled="state.title === '编辑版本'"
        />
      </a-form-item>
      <a-form-item label="版本描述" name="description">
        <a-input v-model:value="formState.description" placeholder="请输入" />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup>
const emit = defineEmits(['refreshTree']);

const formState = reactive({
  name: '',
});

const formRef = ref(null);
const state = reactive({
  openModal: false,
  title: '新增版本',
});

const handleCancel = () => {
  formRef.value.resetFields();
  formState.name = '';
  state.openModal = false;
};

const handleVersion = () => {
  formRef.value
    .validate()
    .then(() => {
      let apiUrl;
      if (state.title === '编辑版本') {
        apiUrl = '/admin/dc/data-version/update';
      } else {
        apiUrl = '/admin/dc/data-version/create';
      }
      http.post(apiUrl, {
        id: state.node?.id ? state.node.id : undefined,
        name: formState.name,
        code: formState.code,
        description: formState.description,
      });
      handleCancel();
      emit('refreshTree');
    })
    .catch(error => {
      console.log('error', error);
    });
};

const showModal = (title, node) => {
  state.title = title;
  state.node = node;

  if (title === '编辑版本') {
    formState.name = node.name;
    formState.code = node.code;
    formState.description = node.description;
  } else {
    formState.name = '';
    formState.code = '';
    formState.description = '';
  }
  state.openModal = true;
};

defineExpose({
  showModal,
});
</script>

<style scoped></style>
