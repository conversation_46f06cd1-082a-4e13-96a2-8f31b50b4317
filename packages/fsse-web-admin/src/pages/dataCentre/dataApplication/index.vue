<template>
  <div class="dataManage">
    <div class="headerConBox">
      <div class="header_left">
        <div class="header_left_title">数据应用</div>
      </div>
    </div>
    <Splitpanes class="dataPage no-transition" :dbl-click-splitter="false">
      <Pane size="20">
        <div class="page_left">
          <TreePart
            :showSearch="false"
            :treeData="treeData"
            :getNodeIcon="getNodeIcon"
            :fieldNames="{
              children: 'children',
              title: 'name',
              key: 'id',
            }"
            :actionMenuItems="state.actionMenuItemsArr"
            @select="onSelect"
            @action="onActionClick"
          >
            <template #top>
              <div class="selectBox">
                <a-select
                  placeholder="请选择"
                  v-model:value="state.selectdataBase"
                  style="width: 100%"
                >
                  <a-select-option value="final">final</a-select-option>
                </a-select>
              </div>
            </template>
            <template #bottom>
              <a-button
                type="primary"
                block
                style="margin-top: 8px"
                @click="openVersion"
              >
                <template #icon>
                  <PlusOutlined />
                </template>
                新增版本
              </a-button>
            </template>
          </TreePart>
        </div>
      </Pane>
      <Pane size="80">
        <div class="page_right" id="page_right">
          <!-- 编辑器写sql的区域 可以执行sql 初始化一个编辑器吧 -->
          <div class="sqlbox">
            <div class="sqlcon">
              <div class="sqlTitleBox">
                <div class="sqlTitle">SQL查询：</div>
                <a-button @click="runSQl" type="primary">运行</a-button>
              </div>
              <div class="monacoBox">
                <div class="monaco" id="monaco" ref="monacoRef"></div>
              </div>
            </div>
          </div>
          <!-- 操作表的地方 -->
          <div class="operationTable">
            <div class="table_type">
              <a-tabs v-model:activeKey="state.activeKey" type="card">
                <a-tab-pane key="1" tab="表结构">
                  <a-table
                    :columns="state.columns_table"
                    :dataSource="state.data_table"
                    :pagination="false"
                  ></a-table>
                </a-tab-pane>
                <a-tab-pane key="2" tab="数据预览">
                  <a-table
                    :scroll="{ x: true }"
                    @change="handleTableChange"
                    :columns="state.columns_preview"
                    :dataSource="state.data_preview"
                    :pagination="state.pagination"
                  ></a-table>
                </a-tab-pane>
                <a-tab-pane key="3" tab="数据导出">
                  <div
                    class="page_no_data"
                    style="
                      display: flex;
                      align-items: center;
                      justify-content: center;
                      flex-direction: column;
                      height: 100%;
                    "
                  >
                    <img
                      src="/noData.png"
                      alt="暂无数据"
                      width="180"
                      height="180"
                    />
                    <div
                      style="
                        font-weight: 400;
                        font-size: 14px;
                        color: rgba(0, 0, 0, 0.65);
                      "
                    >
                      暂无数据，请导出数据。
                    </div>
                  </div>

                  <Teleport to="#page_right" v-if="state.activeKey == 3">
                    <div class="upload_warp">
                      <a-button
                        style="margin-left: 8px"
                        @click="exportDatabase"
                      >
                        导出
                      </a-button>
                    </div>
                  </Teleport>
                </a-tab-pane>
              </a-tabs>
            </div>
          </div>
        </div>
      </Pane>
    </Splitpanes>
  </div>
  <UpFinalTableNameModal
    ref="updateTableNameModalRef"
    @refreshTree="setTimeoutRe"
  ></UpFinalTableNameModal>

  <VersionModal
    ref="versionModalRef"
    @refreshTree="setTimeoutRe"
  ></VersionModal>
</template>

<script setup>
import { ExclamationCircleFilled } from '@ant-design/icons-vue';
import { computed, createVNode, nextTick, onMounted } from 'vue';
import { Modal } from 'ant-design-vue';
import * as monaco from 'monaco-editor';
import TreePart from '@/components/common/TreePart/index.vue';
import UpFinalTableNameModal from './upFinalTableNameModal.vue';
import VersionModal from './versionModal.vue';
import { Splitpanes, Pane } from 'splitpanes';
import 'splitpanes/dist/splitpanes.css';

const store = useStore();

let editor = null;
const router = useRouter();
const monacoRef = ref(null);
const databaseList = ref([]);
const versionModalRef = ref(null);
const treeData = ref([]);
const updateTableNameModalRef = ref(null);
const getNodeIcon = () => {
  return 'icon-mulu1 iconTree';
};

const state = reactive({
  pagination: {
    hideOnSinglePage: true,
    total: 0,
    current: 1,
    showQuickJumper: true,
    showLessItems: true,
    showSizeChanger: true,
    showTotal: total => `共 ${total} 条`,
  },
  activeKey: '1',
  code: 'SELECT * FROM',
  selectdataBase: 'final',
  actionMenuItemsArr: [
    {
      label: '编辑',
      key: 'edit',
    },

    {
      label: '删除',
      key: 'delete',
      color: '#F5222D',
    },
  ],

  // 表结构
  columns_table: [],
  data_table: [],
  // 预览
  columns_preview: [],
  data_preview: [],

  fileListOne: [],
  fileListTwo: [],
});

const headers = {
  Authorization: 'Bearer ' + store.accessToken,
};

const actionTwo = computed(() => {
  return `${import.meta.env.VITE_BASE_API}/admin/datasource/client/table/import`;
});

const handleUploadChange = e => {};

const onActionClick = obj => {
  const { actionKey, node } = obj;
  console.log(obj, '点击的节点对象');
  switch (actionKey) {
    case 'edit':
      if (node.isLeaf) {
        versionModalRef.value.showModal('编辑版本', node);
      } else {
        YMessage.warning('终表不允许操作');
        // // 编辑数据库名字
        // updateTableNameModalRef.value.showModal(node);
      }
      break;
    case 'delete':
      if (node.isLeaf) {
        Modal.confirm({
          title: '提示',
          cancelText: '取消',
          okText: '确认',
          icon: createVNode(ExclamationCircleFilled),
          content: '是否确认删除？',
          centered: false,
          onOk: async () => {
            await http.post('/admin/dc/data-version/delete', {
              ids: [node.id],
            });
            YMessage.success('删除成功');
            clientTables(state.selectdataBase);
          },
          onCancel() {},
        });
      } else {
        YMessage.warning('终表不允许操作');
        // Modal.confirm({
        //   title: '提示',
        //   cancelText: '取消',
        //   okText: '确认',
        //   icon: createVNode(ExclamationCircleFilled),
        //   content: '是否确认删除？',
        //   centered: false,
        //   onOk: async () => {
        //     await http.post('/admin/datasource/client/table/drop', {
        //       code: state.selectdataBase,
        //       tableName: node.name,
        //     });
        //     YMessage.success('删除成功');
        //     clientTables(state.selectdataBase);
        //   },
        //   onCancel() {},
        // });
      }

      break;
    default:
      break;
  }
};

// 查询表结构
const getTableFields = async () => {
  try {
    const { code, data } = await http.get(
      '/admin/datasource/client/table/fields',
      {
        code: state.selectdataBase,
        tableName: state.checkedTableName,
      }
    );
    const { data: result, fields } = data;
    state.columns_table = fields.map((title, idx) => {
      return {
        title,
        dataIndex: title,
        key: idx,
      };
    });
    state.data_table = result.map((item, idx) => {
      let obj = {};
      item.forEach((val, index) => {
        obj[fields[index]] = val == null ? 'Null' : val;
      });
      return obj;
    });
  } catch (error) {
    console.log(error, '表结构失败');
  }
};

// 导出数据库
const exportDatabase = async () => {
  try {
    const { data } = await http.download(
      `/admin/datasource/client/table/export`,
      {
        code: state.selectdataBase,
        tableName: state.checkedTableName,
        needData: 1,
      },
      state.checkedTableName
    );
    console.log(data);
  } catch (error) {
    console.log('导出数据库失败', error);
  }
};

// 查询表数据
const getTableDate = async (code, versionCode) => {
  try {
    state.code = `SELECT * FROM ${state.checkedTableName} LIMIT 0, 10`;
    editor.setValue(state.code);

    const { data } = await http.get('/admin/datasource/client/table/data', {
      query: state.code,
      code: state.selectdataBase,
      versionCode: versionCode,
    });
    const { data: result, fields, count } = data;

    if (fields && fields.length > 0) {
      state.columns_preview = fields.map((title, idx) => {
        return {
          title,
          dataIndex: title,
          key: idx,
          width: 120,
          ellipsis: true,
        };
      });
    } else {
      state.columns_preview = [];
    }

    if (result && result.length > 0) {
      state.data_preview = result.map((item, idx) => {
        let obj = {};
        item.forEach((val, index) => {
          obj[fields[index]] = val == null ? 'Null' : val;
        });
        return obj;
      });
    } else {
      state.data_preview = [];
    }

    state.pagination.total = +count;

    console.log('表数据', state.data_preview);
  } catch (error) {
    console.log(error, '表数据失败');
  }
};

// sql查询
const runSQl = async () => {
  try {
    if (!editor) return;
    state.activeKey = '2';
    const searchSql = editor.getValue();
    const { data } = await http.get('/admin/datasource/client/table/data', {
      query: searchSql,
      code: state.selectdataBase,
      versionCode: state.versionCode ? state.versionCode : undefined,
      pageSize: state.pagination.pageSize,
      pageNo: state.pagination.current,
    });
    const { data: result, fields, count } = data;
    if (fields && fields.length > 0) {
      state.columns_preview = fields.map((title, idx) => {
        return {
          title,
          dataIndex: title,
          key: idx,
          width: 120,
          ellipsis: true,
        };
      });
    } else {
      state.columns_preview = [];
    }

    if (result && result.length > 0) {
      state.data_preview = result.map((item, idx) => {
        let obj = {};
        item.forEach((val, index) => {
          obj[fields[index]] = val == null ? 'Null' : val;
        });
        return obj;
      });
    } else {
      state.data_preview = [];
    }

    state.pagination.total = +count;
    console.log('表数据', state.data_preview);
  } catch (error) {
    console.log(error, '表数据失败');
  }
};

const onSelect = (keys, info) => {
  console.log(keys, info);
  const selected = info.selected;
  state.versionCode = info.node?.code;
  if (selected) {
    // if (state.checkedTableName == info.node.name) {
    //   return;
    // }
    // state.checkedTableName = info.node.name;
    // 点击表 要立马查询表结构
    getTableFields();
    // 查询表数据
    getTableDate(info.node.name, state.versionCode);
  }
};

// 根据数据库code接口获取数据表

const clientTables = async val => {
  try {
    // 同时发起两个并行请求提升效率
    const [tablesRes, versionsRes] = await Promise.all([
      http.get('/admin/datasource/client/tables', {}),
      http.post('/admin/dc/data-version/list', {}),
    ]);

    // 处理主表数据 (保证数组结构)
    const flatArr = tablesRes.data.data.flat();
    if (!flatArr.length) return; // 空数据保护

    // 构建树形结构
    treeData.value = [
      {
        id: flatArr[0],
        name: flatArr[0],
        children: versionsRes.data || [], // 确保 children 为数组
      },
    ];

    if (!state.checkedTableName) {
      state.checkedTableName = treeData.value[0]?.name;

      // 点击表 要立马查询表结构
      getTableFields();
      // 查询表数据
      getTableDate(state.checkedTableName);
    }
  } catch (error) {
    console.error('数据获取失败:', error);
    // 这里可添加 UI 错误提示，例如：
    // message.error('数据加载失败，请稍后重试')
  }
};

// 获取所有的数据库
const getdataSourceList = () => {
  // http.get('/admin/datasource/list').then(res => {
  //   databaseList.value = res.data;

  //   // 默认获取第一个数据库下面的数据表
  //   state.selectdataBase = res.data[0]?.code;
  //   clientTables(state.selectdataBase);
  // });

  clientTables(state.selectdataBase);
};

const initMonaco = cb => {
  if (!monacoRef.value) return; // Only initialize if DOM element exists

  editor = monaco.editor.create(monacoRef.value, {
    value: state.code,
    language: 'sql', // 语言
    theme: 'vs-dark',
    automaticLayout: true, // 添加这行代码，表示自动布局
    fontSize: 14, // 字体大小
    lineNumbers: 'on', // 是否启用行号
    folding: true, // 是否启用代码折叠
    foldingStrategy: 'auto', // 代码折叠策略
    wordWrap: 'on', // 自动换行设置
    wrappingIndent: 'indent', // 换行缩进
    formatOnPaste: true, // 粘贴时是否自动格式化
    formatOnType: true, // 输入时是否自动格式化
    dragAndDrop: false, // 是否允许拖放
    cursorStyle: 'line', // 光标样式
    cursorBlinking: 'blink', // 光标闪烁方式
    lineHeight: 24,
    tabSize: 2, // tab缩进长度
    readOnly: false,
    domReadOnly: false,
    minimap: {
      // 关闭小地图
      enabled: false,
    },
    scrollbar: {
      vertical: 'auto', // 垂直滚动条的显示方式
      horizontal: 'auto', // 水平滚动条的显示方式
      verticalScrollbarSize: 2, // 垂直滚动条的宽
      horizontalScrollbarSize: 2, // 水平滚动条的高度
    },
    scrollBeyondLastLine: false, // 设置编辑器是否可以滚动到最后一行之后
  });
  window.dispatchEvent(new Event("resize")); 
  // setTimeout(() => {
  //   editor.layout(monacoRef.value);
  // }, 100);
};

const setTimeoutRe = () => {
  setTimeout(() => {
    clientTables(state.selectdataBase);
  }, 1000);
};

// sql分页
function handleTableChange(res) {
  state.pagination.current = res.current;
  state.pagination.pageSize = res.pageSize;
  runSQl();
}


onMounted(() => {
  getdataSourceList();
  initMonaco();
  window.addEventListener('resize', () => {
    editor?.layout(monacoRef.value);
  });
});

// Watch for changes in the DOM element's existence
// watch(
//   () => monacoRef.value,
//   newVal => {
//     // Initialize Monaco only when DOM element exists and we have a selected algorithm

//     if (newVal) {
//       initMonaco();
//     }
//   }
// );

const openVersion = () => {
  versionModalRef.value.showModal('新增版本');
};
</script>

<style lang="less" scoped>
.headerConBox {
  height: 57px;
  border-bottom: 1px solid #d9d9d9;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-left: 16px;
  padding-right: 16px;
  flex-shrink: 0;
}

.header_left_title {
  font-size: 18px;
  font-weight: 600;
}

.dataManage {
  height: 100%;
  // display: flex;
  // flex-direction: column;
}

.dataPage {
  flex: 1;
  display: flex;
  height: calc(100vh - 165px);

  .page_left {
    flex: 0 0 280px;
    flex-shrink: 0;
    border-right: 1px solid #d9d9d9;
    padding: 16px;
    overflow: auto;
    height: 100%;
  }

  .page_right {
    flex: 1;
    overflow-y: auto;
    position: relative;
    height: 100%;

    // display: flex;
    // flex-direction: column;
    .sqlbox {
      padding: 16px 16px 0 16px;
      // display: flex;

      // flex: 1;
      .sqlcon {
        flex: 1;
        display: flex;
        flex-direction: column;

        .sqlTitleBox {
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding-bottom: 8px;
          .sqlTitle {
            font-size: 14px;
            color: rgba(0, 0, 0, 0.85);
          }
        }

        .monacoBox {
          display: flex;

          .monaco {
            flex: 1;
            height: 200px;
            max-width: 100%;
          }
        }
      }
    }

    .operationTable {
      padding: 16px;

      .table_type {
        :deep(.ant-tabs-nav) {
          padding: 8px 8px 0px 8px;
          background: #f1f5f7;
          .ant-tabs-tab {
            border-radius: 0px;
            border: unset;
            background: #f1f5f7;
          }
          .ant-tabs-tab-active {
            background: #ffffff;
          }
        }
      }
    }

    .upload_warp {
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      display: flex;
      justify-content: flex-end;
      box-shadow: 0px -1px 8px 0px rgba(190, 190, 190, 0.5);
      padding: 14px;
    }
  }

  .upload_table {
    width: 100%;
    display: flex;
    flex-direction: column-reverse;
    background-color: #fff;

    :deep(.ant-upload) {
      width: 100%;
    }
  }
}

.selectBox {
  flex-shrink: 0;
  padding-bottom: 8px;
}

.no-transition .splitpanes__pane {
  transition: none !important;
}
.no-transition .splitpanes__splitter {
  transition: none !important;
}
</style>
