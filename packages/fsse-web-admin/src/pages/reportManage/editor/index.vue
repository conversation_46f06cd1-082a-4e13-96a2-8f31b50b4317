<template>
  <div class="editor">
    <header>
      <p>模版编辑器</p>
      <a-button type="primary" @click="save" v-if="!isPreview">保存</a-button>
    </header>

    <article>
      <div class="left" v-show="!state.leftCollapse && !isPreview">
        <div class="header">
          <span>指标集</span>
          <a-button type="link" @click="toggleCollapse">
            <i class="iconfont icon-shouqi icon"></i>
          </a-button>
        </div>
        <div class="menu_wrap">
          <a-input-search
            class="search_input"
            v-model:value="state.indicatorsName"
            allowClear
            placeholder="请输入关键字搜索"
            @search="getIndicators"
          />
          <a-spin :spinning="state.indicatorsLoading">
            <div v-if="state.indicatorsList.length">
              <ul class="indicators_list">
                <li
                  v-for="(item, index) in state.indicatorsList"
                  @click="openModel(item)"
                  :key="index"
                >
                  <a-tooltip class="params_item" placement="bottomLeft">
                    <template #title>
                      <span> {{ item.code }}</span>
                    </template>
                    <span> {{ item.name }}</span>
                  </a-tooltip>
                </li>
              </ul>
            </div>
            <div class="empty_data" v-else>
              <img
                src="@/assets/images/empty.png"
                width="180"
                height="180"
                alt="empty.png"
              />
              <p>暂无数据</p>
            </div>
          </a-spin>
        </div>

        <a-button
          type="primary"
          class="add_indicators"
          @click="openAddIndicators"
        >
          <i class="iconfont icon-bjmbq-xz icon"></i>
          <span>添加指标</span>
        </a-button>
      </div>
      <div
        class="fixed_collapse_btn"
        v-show="state.leftCollapse && !isPreview"
        @click="toggleCollapse"
      >
        <a-tooltip placement="bottomLeft">
          <template #title>
            <span>展开指标集</span>
          </template>
          <i class="iconfont icon-bjmbq-sq icon"></i>
        </a-tooltip>
      </div>

      <div id="wps_editor"></div>
      <div class="right" v-if="!isPreview">
        <div class="header">参数集</div>
        <div class="params_wrap">
          <a-input-search
            class="search_input"
            v-model:value="state.paramsName"
            allowClear
            placeholder="请输入关键字搜索"
            @search="searchParams"
          />

          <a-spin :spinning="state.paramsLoading">
            <!-- <div class="type_wrap">
              <p :class="{ active: state.activeParams === 'model' }" @click="toggleParamsType('model')">模型参数</p>
              <p :class="{ active: state.activeParams === 'custom' }" @click="toggleParamsType('custom')">自定义参数</p>
            </div> -->

            <div class="params_list" v-if="state.paramsList.length">
              <a-tooltip
                class="params_item"
                placement="bottomLeft"
                v-for="item in state.paramsList"
                :key="item.id"
                @click="insertParams(item)"
              >
                <template #title>
                  <span> {{ item.code }}</span>
                </template>
                <span> {{ item.name }}</span>
              </a-tooltip>
            </div>
            <div class="empty_data" v-else>
              <img
                src="@/assets/images/empty.png"
                width="180"
                height="180"
                alt="empty.png"
              />
              <p>暂无数据</p>
            </div>
          </a-spin>
        </div>
        <a-button type="primary" class="add_params" @click="openParams">
          <i class="iconfont icon-bjmbq-xz icon"></i>
          <span>添加模型参数</span>
        </a-button>
      </div>
    </article>

    <Model ref="modelRef" @confirm="insertIndicator" />
    <AddTargetModal
      ref="addTargetModalRef"
      @confirmBtn="confirmBtn"
    ></AddTargetModal>
    <AddedModalParameter
      ref="addedModalParameterRef"
      @confirmBtn="createModelParams"
    ></AddedModalParameter>
    <CustomModal
      ref="customModalRef"
      @confirmBtn="confirmBtnCustom"
    ></CustomModal>
  </div>
</template>

<script setup>
import { CaretRightOutlined } from '@ant-design/icons-vue';
import Model from './Model.vue';

import AddTargetModal from '@/pages/reportManage/mvcAdmin/component/addTargetModal.vue';
import AddedModalParameter from '@/pages/reportManage/mvcAdmin/component/addedModalParameter.vue';
import CustomModal from '@/pages/reportManage/mvcAdmin/component/customModal.vue';
import { message } from 'ant-design-vue';
import { wpsConfig } from './wps.config';
import WebOfficeSDK from './web-office-sdk-solution-v2.0.6.es.js';

const route = useRoute();
const router = useRouter();

let timeout = null;

const defaultHandler = {
  get: function (target, prop) {
    return prop in target
      ? target[prop]
      : () => {
          message.success(`未知状态${prop}`);
        };
  },
};

// https://solution.wps.cn/docs/web/instance.html#save 文档保存状态
const SAVE_MESSAGE = new Proxy(
  {
    ok: () => message.success('保存成功'),
    nochange: () => message.success('文档无更新，无需保存'),
    SavedEmptyFile: () => message.warning('暂不支持保存空文件'),
    SpaceFull: () => message.error('保存失败，空间已满'),
    QueneFull: () => message.warning('保存中请勿频繁操作'),
    fail: () => message.error('保存失败'),
    updating: () => message.success('文件更新保存中'),
  },
  defaultHandler
);

// 状态码对应code
const STATUS_MAP_CODE = new Proxy(
  {
    0: SAVE_MESSAGE.nochange,
    1: SAVE_MESSAGE.ok,
    2: SAVE_MESSAGE.SavedEmptyFile,
    3: SAVE_MESSAGE.SpaceFull,
    4: SAVE_MESSAGE.QueneFull,
    5: SAVE_MESSAGE.fail,
    6: SAVE_MESSAGE.updating,
    7: SAVE_MESSAGE.ok,
  },
  defaultHandler
);

// *********************
// Hooks Function
// *********************

const modelRef = ref(null);
const instance = ref(null);
const addTargetModalRef = ref(null);
const addedModalParameterRef = ref(null);
const customModalRef = ref(null);

const state = reactive({
  indicatorsName: '',
  indicatorsLoading: false,
  indicatorsList: [],
  paramsLoading: false,
  paramsName: '',
  paramsList: [],
  activeParams: 'model',
  leftCollapse: false,
  openKeys: [],
  detailsObj: {},
  allsuitable: [],
});

const isPreview = computed(() => {
  return route.query.mode === 'preview';
});

// *********************
// Default Function
// *********************

const setReadOnly = async () => {
  if (isPreview.value) {
    await instance.value.ready();
    const app = instance.value.Application;
    // 设置为只读
    await app.ActiveDocument.SetReadOnly({
      Value: true,
    });
  }
};

function debounce(func, wait, immediate) {
  return function () {
    const context = this;
    const args = arguments;

    const later = function () {
      timeout = null;
      if (!immediate) func.apply(context, args);
    };

    const callNow = immediate && !timeout;

    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
    if (callNow) func.apply(context, args);
  };
}

const initWps = async () => {
  console.log(route.query.fileId, 'fileId');

  instance.value = WebOfficeSDK.init({
    ...wpsConfig,
    fileId: route.query.fileId,
    mount: document.querySelector('#wps_editor'),
  });
  setReadOnly();

  instance.value.ApiEvent.AddApiEventListener(
    'DocumentSaveStatus',
    debounce(data => {
      message.destroy();
      STATUS_MAP_CODE[data.status]();
    }, 1500)
  );
};

/** 获取指标 */
const getIndicators = async () => {
  try {
    state.indicatorsLoading = true;
    const params = {
      name: state.indicatorsName || null,
      modelId: route.query.modelId,
    };
    const { data } = await http.post('/admin/rpt/metric/list', params);
    state.indicatorsList = data;
  } finally {
    state.indicatorsLoading = false;
  }
};

/** 获取模型参数 */
const getModelParams = async () => {
  try {
    state.paramsLoading = true;
    const params = {
      name: state.paramsName,
      modelId: route.query.modelId,
    };
    const { data } = await http.post('/admin/rpt/model/parameter/list', params);
    state.paramsList = data || [];
  } finally {
    state.paramsLoading = false;
  }
};

/** 获取模型参数 */
const getCustomParams = async () => {
  try {
    state.paramsLoading = true;
    const params = {
      name: state.paramsName,
      modelId: route.query.modelId,
    };
    const { data } = await http.post(
      '/admin/rpt/model/custom-parameter/list',
      params
    );
    state.paramsList = data || [];
  } finally {
    state.paramsLoading = false;
  }
};

const getDimNums = async () => {
  const res = await http.get('/admin/rpt/template/details', {
    id: route.query.id,
  });

  state.detailsObj = res.data;
};

const getListSectionSubject = () => {
  http.post('/admin/rpt/model/listSectionSubject').then(res => {
    state.allsuitable = res.data;
  });
};

const init = () => {
  getIndicators();
  getCustomParams();
  getDimNums();
  getListSectionSubject();
};

// *********************
// Life Event Function
// *********************

onMounted(() => {
  init();
  initWps();
});

// *********************
// Service Function
// *********************

const openAddIndicators = () => {
  addTargetModalRef.value.showModal('新增指标', null, state.allsuitable);
};

const confirmBtn = async val => {
  console.log(val);
  await http.post('/admin/rpt/metric/create', {
    ...val,
    modelId: route.query.modelId,
  });

  getIndicators();
};

const openModel = record => {
  if (isPreview.value) return;
  modelRef.value.showModal(record, state.detailsObj);
};

// 插入文本
const insertText = async text => {
  await instance.value.ready();
  const app = instance.value.Application;
  const selection = await app.ActiveDocument.ActiveWindow.Selection;
  const range = await selection.Range;
  range.Text = text;
};

/** 插入指标 */
const insertIndicator = record => {
  let text = '';
  if (record.chart === 'table') {
    // Table比较特殊
    // {{#指标编码}}
    // {{@semantics: 语义模板编码, 指标编码,语义脚本参数, 语义模版id}}
    text = `${record.description}{{#${record.indicatorCode}}}\r\n{{@semantics: ${record.semanticCode}, ${record.indicatorCode}, ${record.scriptId}, ${record.templateId}}}`;
  } else {
    // {{@chart: 图表类型, 指标编码}}
    // {{@semantics: 语义模板编码, 指标编码,语义脚本参数, 语义模版id}}
    text = `${record.description}{{@chart: ${record.chart}, ${record.indicatorCode}, ${record.chartConfigurationId}}}\r\n{{@semantics: ${record.semanticCode}, ${record.indicatorCode}, ${record.semanticsConfigId}}}`;
  }
  insertText(text);
};

/** 插入参数 */
const insertParams = record => {
  if (isPreview.value) return;
  // {{案例code}}
  const text = `{{${record.code}}}`;
  insertText(text);
};

const searchParams = () => {
  getCustomParams();
};

const confirmBtnCustom = (type, obj) => {
  if (type === 'add') {
    http
      .post('/admin/rpt/model/custom-parameter/create', {
        ...obj,
        modelId: route.query.modelId,
      })
      .then(() => {
        message.success('操作成功！');
        getCustomParams();
      });
  }
};

// const toggleParamsType = (type) => {
//   if (state.activeParams === type) return
//   state.paramsName = ''
//   state.activeParams = type
//   getCustomParams()
// }

const createModelParams = val => {
  const arr = val.selectedRows.map(item => {
    return {
      ...item,
      datasourceId: val.datasourceId,
      versionCode: val.versionCode,
      modelId: route.query.modelId,
    };
  });
  http.post('/admin/rpt/model/parameter/batch-create', arr).then(() => {
    message.success('操作成功！');
    addedModalParameterRef.value.cancelForm();
    searchParams();
  });
};

const openParams = () => {
  // if (state.activeParams === 'model') {
  //   addedModalParameterRef.value.openModal();
  // } else {
  //   customModalRef.value.openCustomVisible('add');
  // }

  customModalRef.value.showModal('add', null, state.allsuitable);
};

const toggleCollapse = () => {
  state.leftCollapse = !state.leftCollapse;
  nextTick(() => {
    window.dispatchEvent(new Event('resize'));
  });
};

const save = async () => {
  clearTimeout(timeout);
  const result = await instance.value.save();
  message.destroy();
  SAVE_MESSAGE[result.result]();
  setTimeout(() => {
    // 防止 DocumentSaveStatus也打印信息，因为DocumentSaveStatus做了防抖，
    // 保存按钮是主观触发，message需要快速响应，所以在这里打印message
    clearTimeout(timeout);
  }, 500);
};
</script>

<style lang="less">
.editor {
  position: fixed;
  top: 48px;
  left: 0px;
  bottom: 0;
  width: 100%;
  background: #f1f5f7;
  header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    height: 58px;
    background: #ffffff;
    padding: 0 24px;
    margin-bottom: 12px;
    p {
      font-weight: 600;
      font-size: 18px;
      color: rgba(0, 0, 0, 0.85);
    }
    .go_back_title {
      display: flex;
      align-items: center;
    }
  }
  article {
    display: flex;
    height: calc(100vh - 118px);
  }
  .left {
    display: flex;
    flex-direction: column;
    background: red;
    width: 220px;
    height: 100%;
    transition: all 0.5s ease-in;
    background: #ffffff;
    border-radius: 0px 0px 4px 4px;
    border-right: 1px solid #d9d9d9;

    .header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      height: 44px;
      padding: 0 0 0 16px;
      border-bottom: 1px solid #d9d9d9;

      span {
        font-weight: 600;
        font-size: 14px;
        color: rgba(0, 0, 0, 0.85);
      }
      .icon {
        color: rgba(0, 0, 0, 0.85);
      }
    }

    .ant-spin-nested-loading {
      flex: 1;
      overflow-y: auto;
      .ant-spin-container {
        height: 100%;
      }
    }

    .menu_wrap {
      display: flex;
      flex-direction: column;
      overflow: hidden;
      flex: 1;

      .search_input {
        padding: 16px;
      }

      .ant-collapse-borderless {
        background: #ffffff;
      }

      .ant-collapse-item {
        border-bottom: none;
        margin-bottom: 4px;
      }

      .ant-collapse-header {
        padding: 5px 10px 5px 20px;
        background: #f6f6f6;
      }

      .ant-collapse-content-box {
        padding: 0;
      }

      .ant-collapse {
        flex: 1;
        overflow-y: auto;
      }

      .indicators_list {
        margin: 12px 16px;
        li {
          background: #f6f6f6;
          border-radius: 2px;
          margin-bottom: 8px;
          font-weight: 500;
          font-size: 12px;
          color: #242424;
          cursor: pointer;
          &:hover {
            background: #ebfaf5;
            color: #00b781;
          }
          .params_item {
            display: inline-block;
            box-sizing: border-box;
            width: 100%;
            height: 100%;
            line-height: 1.4; // 增加行高以便更好地显示多行文字
            padding: 10px 8px 10px 12px;
            white-space: normal; // 允许文字换行
            word-wrap: break-word; // 长单词自动换行
            word-break: break-all; // 在任意字符间换行
          }
        }
      }
    }

    .add_indicators {
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 14px;
      color: #ffffff;
      margin: 12px 16px;
      .icon {
        margin: 2px 5px 0 0;
      }
    }
  }

  .fixed_collapse_btn {
    position: fixed;
    left: 20px;
    top: 188px;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    background: #ffffff;
    border: 1px solid #d9d9d9;
    border-radius: 32px;
    cursor: pointer;
  }

  #wps_editor {
    flex: 1;
    height: 100%;
    overflow: hidden;
  }

  .right {
    display: flex;
    flex-direction: column;
    width: 220px;
    height: 100%;
    background: #ffffff;
    border-radius: 0px 0px 4px 4px;
    border-left: 1px solid #d9d9d9;

    .header {
      height: 44px;
      border-bottom: 1px solid #d9d9d9;
      font-weight: 600;
      font-size: 14px;
      color: rgba(0, 0, 0, 0.85);
      line-height: 44px;
      padding: 0 16px;
    }

    .ant-spin-nested-loading {
      flex: 1;
      .ant-spin-container {
        height: 100%;
      }
    }

    .params_wrap {
      display: flex;
      flex-direction: column;
      flex: 1;
      padding: 16px;
      overflow: hidden;

      // .type_wrap {
      //   display: grid;
      //   grid-template-columns: repeat(2, 1fr);
      //   height: 32px;
      //   margin: 12px 0;
      //   p {
      //     text-align: center;
      //     line-height: 30px;
      //     cursor: pointer;
      //     border-top: 1px solid #d9d9d9;
      //     border-bottom: 1px solid #d9d9d9;
      //     border-radius: 4px;
      //     &:nth-of-type(1) {
      //       border-left: 1px solid #d9d9d9;
      //       border-radius: 4px 0 0 4px;
      //     }
      //     &:nth-of-type(2) {
      //       border-right: 1px solid #d9d9d9;
      //       border-radius: 0 4px 4px 0;
      //     }
      //   }

      //   .active {
      //     background: #00b781;
      //     color: #ffffff;
      //     border: none;
      //     line-height: 32px;
      //   }
      // }

      .params_list {
        display: flex;
        flex-direction: column;
        height: calc(100% - 56px);
        overflow-y: auto;
        margin: 0 -16px -12px 0;
        padding-right: 16px;
        padding-top: 16px;
        .params_item {
          padding: 10px 8px 10px 12px;
          background: #f6f6f6;
          border-radius: 2px;
          margin-bottom: 8px;
          font-weight: 500;
          font-size: 12px;
          color: #242424;
          cursor: pointer;
          &:hover {
            background: #ebfaf5;
            color: #00b781;
          }
        }
      }
    }

    .empty_data {
      margin-top: -70px;
    }

    .add_params {
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 14px;
      color: #ffffff;
      margin: 12px 16px;

      .icon {
        margin: 2px 5px 0 0;
      }
    }
  }

  .ant-btn-primary[disabled] {
    color: rgba(0, 0, 0, 0.25);
  }

  .empty_data {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    margin-top: -30px;
    p {
      font-size: 12px;
      color: rgba(0, 0, 0, 0.65);
      line-height: 17px;
    }
  }
}
</style>
