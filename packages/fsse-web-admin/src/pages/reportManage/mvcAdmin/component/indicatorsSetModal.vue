<template>
  <a-modal
    v-model:open="state.open"
    :width="800"
    @cancel="handleCancel"
    @ok="handleOk"
  >
    <template #title>
      {{ '关联题库指标' }}
      <span style="font-size: 14px; color: #8c8c8c; padding-left: 8px"
        >若当前参数无需关联题库指标，则可不指定。</span
      >
    </template>
    <!-- 选择机构 -->

    <div class="orgBox">
      <a-form
        :label-col="{
          style: {
            width: '70px',
          },
        }"
      >
        <a-form-item label="机构：" pb16>
          <a-select
            :disabled="state.loadLoading"
            placeholder="请选择机构"
            @change="orgChange"
            :options="state.orgList"
            :fieldNames="{ label: 'name', value: 'id' }"
            v-model:value="state.orgId"
          >
          </a-select>
        </a-form-item>
        <a-form-item label="指标类型：">
          <a-select
            :disabled="state.loadLoading"
            placeholder="请选择指标类型"
            @change="typeChange"
            v-model:value="state.paperCode"
          >
            <a-select-option value="examination">试卷</a-select-option>
            <a-select-option value="questionnaire">问卷</a-select-option>
            <a-select-option value="practicalOperation">实操</a-select-option>
          </a-select>
        </a-form-item>
      </a-form>
    </div>
    <div class="container-box">
      <a-spin :spinning="state.loadLoading">
        <div class="body">
          <div class="left">
            <div style="padding: 16px">
              <a-input
                v-model:value="state.searchValue"
                placeholder="请输入名称"
              >
                <template #suffix>
                  <SearchOutlined />
                </template>
              </a-input>
            </div>

            <div style="width: 100%">
              <a-row style="max-height: 400px; overflow-y: auto">
                <a-col
                  :span="24"
                  style="padding: 8px 0; display: flex; align-items: center"
                  v-for="(item, idx) in filteredIndicatorList"
                  :key="item.id"
                >
                  <a-checkbox
                    @change="e => selectItem(e, item)"
                    v-model:checked="item.isSelected"
                    style="padding: 0 8px 0 16px"
                  ></a-checkbox>
                  <a-tooltip :title="item.name">
                    <div
                      @click="selectItemCopy(item)"
                      :class="{
                        ellipsis: true,
                        nameBox: true,
                        selectBox: item.id === state.selectId,
                      }"
                    >
                      {{ item.name }}
                    </div>
                  </a-tooltip>
                </a-col>
              </a-row>
            </div>
          </div>

          <div class="right" style="max-height: 465px; overflow-y: auto">
            <IndicatorTree
              :tree-data="state.evaluatingIndicators"
              v-model:checked-keys="state.checkedKeys"
              v-model:half-checked-keys="state.halfCheckedKeys"
              @checked-change="handleTreeCheckedChange"
            />
          </div>
        </div>
      </a-spin>
    </div>
  </a-modal>
</template>

<script setup>
import { SearchOutlined } from '@ant-design/icons-vue';
import IndicatorTree from './IndicatorTree.vue';

const emit = defineEmits(['submit']);
const props = defineProps({});

// 过滤后的指标列表（支持搜索）
const filteredIndicatorList = computed(() => {
  if (!state.searchValue) {
    return state.indicatorList;
  }
  return state.indicatorList.filter(
    item =>
      item.name &&
      item.name.toLowerCase().includes(state.searchValue.toLowerCase())
  );
});

const state = reactive({
  copyInitData: {},
  open: false,
  orgId: '',
  paperCode: '',
  loadLoading: false,
  searchValue: '',
  userCheckListChild: [],
  evaluatingIndicators: [],
  checkedKeys: [],
  halfCheckedKeys: [], // 半选中的节点
  selectAll: false,
  selectId: null, // 当前选中的指标分组ID
  orgList: [], // 机构列表
  indicatorList: [], // 指标分组列表
  groupSelectedStates: {}, // 每个分组的选中状态 { groupId: { checkedKeys: [], halfCheckedKeys: [] } }

  // 向外部暴露这个组件触发handleOk 时的最终数据
  indicatorData: {
    orgId: null,
    indicatorType: '',
    indicatorGroup: [],
  },
});

const getAllOrg = () => {
  http.post('/admin/org/list', {}).then(res => {
    state.orgList = res.data;
  });
};

const showModal = (initData = null) => {
  // 获取所有机构列表
  getAllOrg();

  // 清除数据
  resetAllStates();

  // 如果有初始化数据，则进行回显
  if (initData) {
    state.copyInitData = initData;
    initializeWithData(initData);
  } else {
    // 清空所有状态
    state.copyInitData = {};
    resetAllStates();
  }

  state.open = true;
};

// 初始化数据（用于编辑回显）
const initializeWithData = async data => {
  if (data.orgId) {
    state.orgId = data.orgId;
  }
  if (data.indicatorType) {
    state.paperCode = data.indicatorType;
  }

  // 获取指标列表，然后设置选中状态
  await getListIndicator();

  if (data.indicatorGroup && data.indicatorGroup.length > 0) {
    // 设置指标分组的选中状态
    data.indicatorGroup.forEach(group => {
      const indicatorItem = state.indicatorList.find(
        item => item.id === group.id
      );
      if (indicatorItem) {
        indicatorItem.isSelected = true;

        // 保存该分组的树形选中状态
        if (group.indicator && group.indicator.length > 0) {
          const { checkedKeys, halfCheckedKeys } = calculateTreeCheckState(
            group.indicator,
            indicatorItem.evaluatingIndicators
          );

          console.log('回显计算结果:', {
            groupId: group.id,
            groupName: group.name,
            selectedData: group.indicator,
            fullTreeData: indicatorItem.evaluatingIndicators,
            checkedKeys,
            halfCheckedKeys,
          });

          state.groupSelectedStates[group.id] = {
            checkedKeys,
            halfCheckedKeys,
          };
        }
      }
    });
  }
};

// 计算树形组件的选中状态（区分全选和半选）
const calculateTreeCheckState = (selectedTreeData, fullTreeData) => {
  const checkedKeys = [];
  const halfCheckedKeys = [];

  // 找出真正的叶子节点（在选中数据中没有子节点的节点）
  const findActualSelectedLeafNodes = nodes => {
    const leafNodes = [];
    const traverse = nodeList => {
      nodeList.forEach(node => {
        if (!node.childs || node.childs.length === 0) {
          leafNodes.push(node.id);
        } else {
          traverse(node.childs);
        }
      });
    };
    traverse(nodes);
    return leafNodes;
  };

  const actualSelectedLeafNodes = findActualSelectedLeafNodes(selectedTreeData);

  // 递归计算完整树中每个节点的状态
  const calculateNodeState = node => {
    if (!node.children || node.children.length === 0) {
      // 叶子节点：检查是否在真正选中的叶子节点中
      if (actualSelectedLeafNodes.includes(node.id)) {
        checkedKeys.push(node.id);
        return { checked: true, hasSelected: true };
      }
      return { checked: false, hasSelected: false };
    }

    // 非叶子节点：根据子节点状态决定
    let allChildrenChecked = true;
    let hasAnySelectedChild = false;

    node.children.forEach(child => {
      const childResult = calculateNodeState(child);
      if (!childResult.checked) {
        allChildrenChecked = false;
      }
      if (childResult.hasSelected) {
        hasAnySelectedChild = true;
      }
    });

    if (!hasAnySelectedChild) {
      // 没有任何子节点被选中
      return { checked: false, hasSelected: false };
    } else if (allChildrenChecked) {
      // 所有子节点都被选中，当前节点全选
      checkedKeys.push(node.id);
      return { checked: true, hasSelected: true };
    } else {
      // 部分子节点被选中，当前节点半选
      halfCheckedKeys.push(node.id);
      return { checked: false, hasSelected: true };
    }
  };

  // 从完整树的根节点开始计算
  if (fullTreeData && fullTreeData.length > 0) {
    fullTreeData.forEach(rootNode => {
      calculateNodeState(rootNode);
    });
  }

  return { checkedKeys, halfCheckedKeys };
};

// 重置所有状态
const resetAllStates = () => {
  state.orgId = null;
  state.paperCode = null;
  state.searchValue = '';
  state.selectId = null;
  state.checkedKeys = [];
  state.halfCheckedKeys = [];
  state.evaluatingIndicators = [];
  state.groupSelectedStates = {};
  state.indicatorList = [];

  // 清空指标列表的选中状态
  // if (state.indicatorList && state.indicatorList.length > 0) {
  //   state.indicatorList.forEach(item => {
  //     item.isSelected = false;
  //   });
  // }
};

// 这个接口贼慢贼慢 加loading吧
// 获取题库指标列表
const getListIndicator = async () => {
  try {
    state.indicatorList = [];
    state.evaluatingIndicators = [];
    state.loadLoading = true;
    const res = await http.post('/admin/rpt/metric/listIndicator', {
      paperCode: state.paperCode,
      orgId: state.orgId,
    });

    state.loadLoading = false;
    state.indicatorList = res.data;

    if (
      state.copyInitData.indicatorGroup &&
      state.copyInitData.indicatorGroup.length > 0
    ) {
      // 设置指标分组的选中状态
      state.copyInitData.indicatorGroup.forEach(group => {
        const indicatorItem = state.indicatorList.find(
          item => item.id === group.id
        );
        if (indicatorItem) {
          indicatorItem.isSelected = true;

          // 保存该分组的树形选中状态
          if (group.indicator && group.indicator.length > 0) {
            const { checkedKeys, halfCheckedKeys } = calculateTreeCheckState(
              group.indicator,
              indicatorItem.evaluatingIndicators
            );

            console.log('回显计算结果:', {
              groupId: group.id,
              groupName: group.name,
              selectedData: group.indicator,
              fullTreeData: indicatorItem.evaluatingIndicators,
              checkedKeys,
              halfCheckedKeys,
            });

            state.groupSelectedStates[group.id] = {
              checkedKeys,
              halfCheckedKeys,
            };
          }
        }
      });
    }
  } catch (error) {
    state.loadLoading = false;
  }
};

const orgChange = (val, opt) => {
  state.orgName = opt.name;
  if (state.orgId && state.paperCode) {
    getListIndicator();
  }
};

const typeChange = () => {
  if (state.orgId && state.paperCode) {
    getListIndicator();
  }
};

// 选择指标查询指标详情 如果是取消选中的操作 则不查详情
const selectItem = (e, item) => {
  if (e.target.checked) {
    // 保存当前分组的选中状态
    saveCurrentGroupState();

    // 切换到新的分组
    state.selectId = item.id;
    state.evaluatingIndicators = item.evaluatingIndicators;

    // 恢复该分组的选中状态
    restoreGroupState(item.id);
  } else {
    // 取消选中时，清除该分组的选中状态
    if (state.groupSelectedStates[item.id]) {
      delete state.groupSelectedStates[item.id];
    }
  }
};

// 只负责点击发送查详情的接口 ,禁止记录选中状态
const selectItemCopy = item => {
  // 保存当前分组的选中状态
  saveCurrentGroupState();

  // 切换到新的分组
  state.selectId = item.id;
  state.evaluatingIndicators = item.evaluatingIndicators;

  // 恢复该分组的选中状态
  restoreGroupState(item.id);
};

// 保存当前分组的选中状态
const saveCurrentGroupState = () => {
  if (
    state.selectId &&
    (state.checkedKeys.length > 0 || state.halfCheckedKeys.length > 0)
  ) {
    state.groupSelectedStates[state.selectId] = {
      checkedKeys: [...state.checkedKeys],
      halfCheckedKeys: [...state.halfCheckedKeys],
    };
  }
};

// 恢复分组的选中状态
const restoreGroupState = groupId => {
  if (state.groupSelectedStates[groupId]) {
    state.checkedKeys = [...state.groupSelectedStates[groupId].checkedKeys];
    state.halfCheckedKeys = [
      ...state.groupSelectedStates[groupId].halfCheckedKeys,
    ];
  } else {
    // 如果没有保存的状态，则清空选中
    state.checkedKeys = [];
    state.halfCheckedKeys = [];
  }
};

// 处理树形组件选中状态变化
const handleTreeCheckedChange = data => {
  // 实时更新当前分组的选中状态
  if (state.selectId) {
    state.groupSelectedStates[state.selectId] = {
      checkedKeys: [...data.checkedKeys],
      halfCheckedKeys: [...data.halfCheckedKeys],
    };
  }
  console.log('树形组件选中状态变化:', data);
};

// 获取选中的指标分组数据
const getSelectedIndicatorGroups = () => {
  return state.indicatorList.filter(item => item.isSelected);
};

// 构建最终的指标数据结构
const buildIndicatorData = () => {
  // 保存当前分组的选中状态
  saveCurrentGroupState();

  const selectedGroups = getSelectedIndicatorGroups();
  const indicatorGroup = [];

  selectedGroups.forEach(group => {
    // 获取该分组对应的树形选中数据
    const treeSelectedNodes = getTreeSelectedNodes(group);

    if (treeSelectedNodes.length > 0) {
      indicatorGroup.push({
        id: group.id,
        name: group.name,
        indicator: treeSelectedNodes,
      });
    }
  });

  return {
    orgId: state.orgId,
    orgName: state.orgName,
    indicatorType: state.paperCode,
    indicatorGroup: indicatorGroup,
  };
};

// 获取树形组件选中的节点数据（保持树形结构）
const getTreeSelectedNodes = group => {
  // 从保存的状态中获取该分组的选中数据
  const groupState = state.groupSelectedStates[group.id];
  if (!groupState) {
    return [];
  }

  const allSelectedIds = [
    ...new Set([...groupState.checkedKeys, ...groupState.halfCheckedKeys]),
  ];

  if (!group.evaluatingIndicators || allSelectedIds.length === 0) {
    return [];
  }

  // 递归构建树形结构，只包含选中的节点及其必要的父节点
  const buildTreeStructure = nodes => {
    const result = [];

    nodes.forEach(node => {
      const isCurrentNodeSelected = allSelectedIds.includes(node.id);

      // 先递归处理子节点
      let childNodes = [];
      if (node.children && node.children.length > 0) {
        childNodes = buildTreeStructure(node.children);
      }

      // 如果当前节点被选中，或者有选中的子节点，则包含此节点
      if (isCurrentNodeSelected || childNodes.length > 0) {
        const nodeData = {
          id: node.id,
          name: node.name,
          childs: childNodes,
        };

        result.push(nodeData);
      }
    });

    return result;
  };

  return buildTreeStructure(group.evaluatingIndicators);
};

const handleCancel = () => {
  state.open = false;
};

const handleOk = () => {
  // 构建最终数据
  const finalData = buildIndicatorData();

  // 更新 state.indicatorData
  state.indicatorData = finalData;

  // 发送数据给父组件
  emit('submit', finalData);

  // 关闭模态框
  state.open = false;

  console.log('最终输出的指标数据:', finalData);
};

defineExpose({
  showModal,
});
</script>

<style lang="less" scoped>
.orgBox {
  padding: 16px 16px 0px 16px;
}
.container-box {
  position: relative;
  padding: 16px 16px;
  .body {
    height: 100%;
    min-height: 287px;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    display: flex;
    .left {
      width: 250px;

      border-right: 1px solid #d9d9d9;
    }
    .right {
      flex: 1;
      padding: 16px;
    }
  }
}

.nameBox {
  cursor: pointer;
  flex: 1;
  padding: 5px 12px;
  // display: inline-block;
  // max-width: 70%;
}

.selectBox {
  background: rgba(0, 183, 129, 0.1);
  border-radius: 4px;
  color: #00b781;
}
</style>
