<template>
  <div>
    <a-modal
      v-model:open="state.customVisible"
      :title="comTitle"
      :confirmLoading="state.customLoading"
      :maskClosable="false"
      :keyboard="false"
      :body-style="{ maxHeight: '550px', overflow: 'auto', padding: '24px' }"
      @cancel="customcancelForm"
      @ok="customhandleOk"
      width="570px"
      okText="确认"
      cancelText="取消"
    >
      <div>
        <div class="tipsBox">
          自定义参数为每份报告中指定内容的占位符，如：参与监测的学校“&#123;&#123;school_id&#125;&#125;”，每份报告中学校名称不同，则使用school_id占位。
        </div>
        <a-form
          ref="customRef"
          layout="vertical"
          :model="state.customObj"
          name="customObj"
          :label-col="{ span: 24 }"
          :wrapper-col="{ span: 24 }"
          autocomplete="off"
        >
          <a-form-item
            label="参数编码："
            name="code"
            mb24
            :rules="[
              {
                required: true,
                message: '请输入指标编码',
              },
            ]"
          >
            <a-input
              v-model:value.trim="state.customObj.code"
              placeholder="请输入"
              show-count
              :maxlength="20"
            ></a-input>
          </a-form-item>
          <a-form-item
            label="参数名称："
            name="name"
            mb24
            :rules="[
              {
                required: true,
                message: '请输入指标名称',
              },
            ]"
          >
            <a-input
              placeholder="请输入"
              v-model:value.trim="state.customObj.name"
              show-count
              :maxlength="20"
            ></a-input>
          </a-form-item>

          <a-form-item
            label="描述："
            name="description"
            mb24
            :rules="[
              {
                required: true,
                message: '请输入描述',
              },
            ]"
          >
            <a-input
              placeholder="请输入"
              v-model:value.trim="state.customObj.description"
            ></a-input>
          </a-form-item>
          <a-form-item
            help="说明：若为通用，则可不选择适用对象"
            label="适用对象："
            name="suitable"
            mb24
          >
            <a-table
              :columns="columns"
              :data-source="state.customObj.suitable"
              :pagination="false"
              bordered
            >
              <template #bodyCell="{ record, column, index }">
                <a-form-item
                  v-if="column.key == 'grade'"
                  :name="column.dataIndex"
                >
                  <a-select
                    :fieldNames="{ label: 'name', value: 'id' }"
                    allowClear
                    :options="state.gradeArr"
                    v-model:value="record[column.dataIndex]"
                    placeholder="请选择"
                    @change="val => gradeChange(val, record)"
                  >
                  </a-select>
                </a-form-item>
                <a-form-item
                  v-if="column.key == 'subject'"
                  :name="column.dataIndex"
                  :rules="[{ required: false, message: '请选择' }]"
                >
                  <div style="display: flex; align-items: center">
                    <a-select
                      v-model:value="record[column.dataIndex]"
                      placeholder="请选择"
                      mode="multiple"
                      :options="record.subjectArr || []"
                      :fieldNames="{
                        label: 'subjectName',
                        value: 'subjectCode',
                      }"
                    >
                    </a-select>
                    <div
                      style="
                        display: flex;
                        align-items: center;
                        margin-left: 8px;
                      "
                    >
                      <PlusCircleOutlined
                        @click="addSuitable"
                        style="
                          padding: 0 8px;
                          font-size: 18px;
                          cursor: pointer;
                          color: #11c685;
                        "
                      />
                      <template v-if="index != 0">
                        <MinusCircleOutlined
                          @click="removeSuitable(index)"
                          style="
                            font-size: 18px;
                            cursor: pointer;
                            color: #f5222d;
                          "
                        />
                      </template>
                      <template v-else>
                        <span style="display: inline; width: 18px" />
                      </template>
                    </div>
                  </div>
                </a-form-item>
              </template>
            </a-table>
          </a-form-item>
        </a-form>
      </div>
    </a-modal>
  </div>
</template>

<script setup>
const columns = [
  { title: '适用年级', dataIndex: 'grade', key: 'grade', width: 190 },
  { title: '适用学科', dataIndex: 'subject', key: 'subject' },
];

const emit = defineEmits(['confirmBtn']);

const comTitle = computed(() => {
  return state.customType === 'add' ? '新增自定义参数' : '编辑自定义参数';
});

const customRef = ref(null);
const state = reactive({
  gradeArr: [],
  customType: 'add',
  customVisible: false,
  customLoading: false,
  allsuitable: [],
  customObj: {
    suitable: [{}],
  },
});

const customcancelForm = () => {
  state.customVisible = false;
  customRef.value.resetFields();
};

const customhandleOk = () => {
  customRef.value.validateFields().then(() => {
    let finalObj = {
      ...state.customObj,
      suitable: null,
    };

    if (JSON.stringify(arrToObj(state.customObj.suitable)) !== '{}') {
      finalObj.suitable = JSON.stringify(arrToObj(state.customObj.suitable));
    }

    emit('confirmBtn', state.customType, finalObj);
    customcancelForm();
  });
};

function findSubjectsByGradeId(arr, gradeId) {
  // 使用find方法查找包含目标gradeId的学段
  const parentSection = arr.find(section =>
    section.gradeInfos?.some(grade => grade.id === gradeId)
  );

  // 返回匹配学段的subjects数组（空值安全处理）
  return parentSection?.subject || [];
}

// 将 obj 转换为 arr
function objToArr(obj) {
  return Object.keys(obj).map(grade => ({
    grade: grade,
    subject: obj[grade],
    subjectArr: findSubjectsByGradeId(state.allsuitable, grade),
  }));
}

function combineGradeInfos(arr) {
  return arr.reduce((result, item) => {
    return result.concat(item.gradeInfos || []); // 兼容无gradeInfos的情况
  }, []);
}

// 将 arr 转换为 obj
function arrToObj(arr) {
  return arr.reduce((acc, item) => {
     if (item.grade) {
      acc[item.grade] = item.subject;
    }
    return acc;
  }, {});
}

const showModal = (type, record, allsuitable) => {
  state.customType = type;

  state.allsuitable = allsuitable || [];
  let itemObj = null;
  if (record) {
    // 深拷贝
    itemObj = JSON.parse(JSON.stringify(record));
  }

  // 处理出年级的数组数据
  state.gradeArr = combineGradeInfos(allsuitable);
  if (itemObj) {
    // 如果带了编辑的信息进来就回显赋值
    // 这里是外部传了详情信息进来回显
    state.customObj.code = itemObj.code;
    state.customObj.id = itemObj.id;
    state.customObj.name = itemObj.name;
    state.customObj.description = itemObj.description;
    if (itemObj.suitable) {
      state.customObj.suitable = objToArr(JSON.parse(itemObj.suitable)); // 字符串改成数组回显
    } else {
      state.customObj.suitable = [{}];
    }

    console.log(state.customObj.suitable, 'state.customObj.suitable');
  } else {
    // 如果是新增则初始化一份数据
    state.customObj = {
      suitable: [{}],
    };
  }

  state.customVisible = true;
};

const compileObj = val => {
  state.customObj = val;
};

const addSuitable = () => {
  state.customObj.suitable.push({});
};

const removeSuitable = index => {
  state.customObj.suitable.splice(index, 1);
};

// 筛选出学科
const gradeChange = (val, item) => {
  item.subjectArr = [];
  item.subject = [];
  item.subjectArr = findSubjectsByGradeId(state.allsuitable, val);
};

defineExpose({ showModal, compileObj });
</script>

<style lang="less" scoped>
.tipsBox {
  font-weight: 400;
  font-size: 14px;
  color: #f58622;
  line-height: 20px;
  text-align: left;
  font-style: normal;
  padding-bottom: 12px;
}
</style>
