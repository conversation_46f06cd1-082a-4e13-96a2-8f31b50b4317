<template>
  <div style="padding: 20px;">
    <h2>指标设置组件测试</h2>

    <div style="margin-bottom: 20px;">
      <a-button type="primary" @click="openModal">打开指标设置模态框</a-button>
      <a-button style="margin-left: 10px;" @click="openModalWithData">打开模态框（带初始数据）</a-button>
    </div>

    <div style="margin-bottom: 20px;">
      <h3>测试说明：</h3>
      <p>1. 点击"打开模态框（带初始数据）"按钮</p>
      <p>2. 应该看到"指标a"被选中</p>
      <p>3. 右侧树形组件中：</p>
      <ul>
        <li>"三级指标2"和"二级指标3"应该显示为<strong>全选状态</strong>（蓝色勾选）</li>
        <li>"通用体系大青蛙"和"二级指标1"应该显示为<strong>半选状态</strong>（蓝色方块）</li>
        <li>其他节点应该是未选中状态</li>
      </ul>
    </div>

    <div style="margin-bottom: 20px;">
      <h3>预期的数据结构示例：</h3>
      <p>当选中 "三级指标2" 和 "二级指标3" 时，应该输出保持树形结构的数据：</p>
      <pre style="background: #f0f0f0; padding: 10px; border-radius: 4px; font-size: 12px;">{{expectedDataStructure}}</pre>
    </div>

    <div v-if="resultData">
      <h3>最终输出数据：</h3>
      <pre style="background: #f5f5f5; padding: 10px; border-radius: 4px;">{{ JSON.stringify(resultData, null, 2) }}</pre>
    </div>

    <!-- 指标设置模态框 -->
    <IndicatorsSetModal
      ref="indicatorsSetModalRef"
      @submit="handleSubmit"
    />
  </div>
</template>

<script setup>
import IndicatorsSetModal from './indicatorsSetModal.vue';

const indicatorsSetModalRef = ref(null);
const resultData = ref(null);

// 预期的数据结构示例
const expectedDataStructure = `{
  "orgId": "100040",
  "indicatorType": "examination",
  "indicatorGroup": [
    {
      "id": "1872567273281687554",
      "name": "指标a",
      "indicator": [
        {
          "id": "1621456386981855555",
          "name": "通用体系大青蛙",
          "childs": [
            {
              "id": "162145643511982434",
              "name": "二级指标1",
              "childs": [
                {
                  "id": "1914127585730781185",
                  "name": "三级指标2"
                }
              ]
            },
            {
              "id": "1890303801139675137",
              "name": "二级指标3"
            }
          ]
        }
      ]
    }
  ]
}`;

// 打开模态框
const openModal = () => {
  indicatorsSetModalRef.value?.showModal();
};

// 打开模态框（带初始数据）
const openModalWithData = () => {
  const initData = {
    "orgId": "100040",
    "indicatorType": "examination",
    "indicatorGroup": [
        {
            "id": "1872567273281687554",
            "name": "指标a",
            "indicator": [
                {
                    "id": "1621456386981855555",
                    "name": "通用体系大青蛙",
                    "childs": [
                        {
                            "id": "162145643511982434",
                            "name": "二级指标1",
                            "childs": [
                                {
                                    "id": "1914127585730781185",
                                    "name": "三级指标2",
                                    "childs": []
                                }
                            ]
                        },
                        {
                            "id": "1622488325499092991",
                            "name": "二级指标2",
                            "childs": []
                        },
                        {
                            "id": "1890303801139675137",
                            "name": "二级指标3",
                            "childs": []
                        }
                    ]
                }
            ]
        },
        {
            "id": "1891737273502949377",
            "name": "复测我",
            "indicator": [
                {
                    "id": "1892043434932461570",
                    "name": "1124",
                    "childs": [
                        {
                            "id": "1892043454448558082",
                            "name": "2",
                            "childs": [
                                {
                                    "id": "1892043472735723522",
                                    "name": "3",
                                    "childs": []
                                }
                            ]
                        }
                    ]
                }
            ]
        },
        {
            "id": "1892043507246456834",
            "name": "测试",
            "indicator": [
                {
                    "id": "1892046334551285762",
                    "name": "123",
                    "childs": [
                        {
                            "id": "1892047895486685186",
                            "name": "123",
                            "childs": []
                        }
                    ]
                },
                {
                    "id": "1892050866945548290",
                    "name": "123",
                    "childs": []
                },
                {
                    "id": "1892050891587084290",
                    "name": "2222",
                    "childs": []
                }
            ]
        },
        {
            "id": "1892755295432634370",
            "name": "测试新增指标1211",
            "indicator": [
                {
                    "id": "1892755417432354818",
                    "name": "我还是一级指标",
                    "childs": [
                        {
                            "id": "1892755528090677250",
                            "name": "二级",
                            "childs": [
                                {
                                    "id": "1892755602208223233",
                                    "name": "三级",
                                    "childs": []
                                }
                            ]
                        }
                    ]
                }
            ]
        }
    ]
}

  indicatorsSetModalRef.value?.showModal(initData);
};

// 处理提交数据
const handleSubmit = (data) => {
  resultData.value = data;
  console.log('接收到的数据:', data);
};
</script>

<style scoped>
pre {
  white-space: pre-wrap;
  word-wrap: break-word;
}
</style>
