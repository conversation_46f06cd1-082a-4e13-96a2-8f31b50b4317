<!-- 详情的页面 -->
<template>
  <div>
    <div class="particularsSearch">
      <searchForm
        v-model:formState="state.targetQueryForm"
        :formList="formList"
        @submit="targetqueryList"
        @reset="targetresetForm"
      ></searchForm>

      <div class="btn-wrap" pb16 flex flex-justify-end>
        <a-button type="primary" @click="addbtn">新增</a-button>
        <a-button :disabled="state.checkTableArr.length === 0" @click="delbtn"
          >删除</a-button
        >
      </div>
    </div>
    <div class="particularsTable">
      <ETable
        hash="targetPage_tableBox"
        :loading="state.tableLoading"
        :columns="targetColumns"
        :dataSource="state.tableSource"
        :total="+state.paginations.total"
        @paginationChange="handleTableChange"
        :current="state.paginations.pageNo"
        :row-selection="{
          selectedRowKeys: state.checkTableArr,
          onChange: tableSelectChange,
        }"
        :scroll="{ x: true }"
      >
        <!-- 查看关联题库指标 -->
        <template #correlation="{ record }">
          <a-button
            class="btn-link-color"
            type="link"
            @click="lookAssociation(record)"
            >查看</a-button
          >
        </template>
        <!--  -->

        <template #suitable="{ record }">
          <span :title="convert(record.suitable)">
            {{ convert(record.suitable) || '通用' }}
          </span>
        </template>

        <template #operation="{ record }">
          <a-button type="link" class="btn-link-color" @click="compile(record)"
            >编辑</a-button
          >
          <a-button
            type="link"
            class="btn-link-color"
            @click="previewVisible(record)"
            >预览</a-button
          >
        </template>
      </ETable>
    </div>
    <!--弹窗的组件-->
    <div>
      <!-- 新增弹窗 -->
      <AddTargetModal
        ref="addTargetModalRef"
        @confirmBtn="confirmBtn"
      ></AddTargetModal>
      <!-- 预览弹窗 -->
      <a-modal
        :body-style="{ maxHeight: '660px', overflow: 'auto', padding: '24px' }"
        v-model:open="state.previewVisible"
        title="预览"
        :footer="null"
        :maskClosable="false"
        :keyboard="false"
        centered
        width="570px"
      >
        <div class="preName">
          指标：<span class="preName_item">{{ editObj.name }}</span>
        </div>
        <div
          class="preName"
          v-for="(item, index) in editObj.modelMetricItemList"
        >
          项{{ index + 1 }}：<span class="preName_item">{{ item.name }}</span>
        </div>
      </a-modal>
      <!-- 编辑弹窗 -->
      <a-modal
        v-model:open="state.editVisible"
        title="编辑"
        :confirmLoading="state.editLoading"
        :maskClosable="false"
        :keyboard="false"
        centered
        @cancel="editcancelForm"
        @ok="edithandleOk"
        width="570px"
        okText="确认"
        cancelText="取消"
        :body-style="{ maxHeight: '660px', overflow: 'auto', padding: '24px' }"
      >
        <div>
          <a-form
            ref="editRef"
            layout="vertical"
            :model="editObj"
            name="editObj"
            :label-col="{ span: 24 }"
            :wrapper-col="{ span: 24 }"
            autocomplete="off"
          >
            <a-form-item
              mb24
              label="数据源："
              name="datasourceName"
              :rules="[
                {
                  required: true,
                  message: '请选择数据源',
                },
              ]"
            >
              <a-select
                v-model:value="editObj.datasourceName"
                placeholder="请选择"
                :disabled="true"
                :options="state.sourceListArr"
                :showArrow="true"
                :field-names="{
                  label: 'name',
                  value: 'code',
                }"
              >
              </a-select>
            </a-form-item>
            <a-form-item
              mb24
              label="版本号："
              name="versionCode"
              :rules="[
                {
                  required: true,
                  message: '请选择版本号',
                },
              ]"
            >
              <a-select
                v-model:value="editObj.versionCode"
                placeholder="请选择"
                :disabled="true"
                :options="state.dataVersionsArr"
                :showArrow="true"
                :field-names="{
                  label: 'name',
                  value: 'code',
                }"
              >
              </a-select>
            </a-form-item>

            <a-form-item mb24 label="指标编码：" name="code">
              <a-input
                :disabled="true"
                placeholder="请输入"
                v-model:value.trim="editObj.code"
              />
            </a-form-item>
            <a-form-item mb24 label="指标名称：" name="name">
              <a-input
                :disabled="true"
                placeholder="请输入"
                v-model:value.trim="editObj.name"
              />
            </a-form-item>
            <a-form-item
              mb24
              label="分类："
              name="typeCode"
              :rules="[
                {
                  required: true,
                  message: '请选择分类',
                },
              ]"
            >
              <a-select
                v-model:value="editObj.typeCode"
                placeholder="请选择"
                :disabled="true"
                :options="state.classifyArr"
                :showArrow="true"
                :field-names="{
                  label: 'typeName',
                  value: 'typeCode',
                }"
              >
              </a-select>
            </a-form-item>
            <a-form-item label="描述：" name="description">
              <a-input
                placeholder="请输入"
                v-model:value.trim="editObj.description"
              />
            </a-form-item>
          </a-form>
        </div>
      </a-modal>
      <!-- 查看关联题库指标 -->
      <IndicatorsLookModal ref="indicatorsLookModalRef"></IndicatorsLookModal>
    </div>
  </div>
</template>

<script setup name="targetPageCom">
import { createVNode, ref } from 'vue';

import AddTargetModal from './addTargetModal.vue';
import IndicatorsLookModal from './indicatorsLookModal.vue';
import { Session } from '@/utils/storage';
import { message, Modal } from 'ant-design-vue';
import { ExclamationCircleFilled } from '@ant-design/icons-vue';

const props = defineProps({
  allsuitable: {
    type: Array,
    default: () => [],
  },
});

const indicatorsLookModalRef = ref(null);
const targetColumns = [
  {
    title: '指标编码',
    dataIndex: 'code',
    key: 'code',
  },
  {
    title: '指标名称',
    dataIndex: 'name',
    key: 'name',
  },
  {
    title: '描述',
    dataIndex: 'description',
    key: 'description',
    width: '150px',
  },
  {
    title: '关联题库指标',
    dataIndex: 'correlation',
    key: 'correlation',
    width: '200px',
  },
  {
    title: '适用对象',
    dataIndex: 'suitable',
    key: 'suitable',
  },
  // {
  //   title: '分类',
  //   dataIndex: 'typeName',
  //   key: 'typeName',
  // },
  {
    title: '创建人',
    dataIndex: 'createBy',
    key: 'createBy',
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    key: 'createTime',
  },
  {
    title: '操作',
    key: 'operation',
    dataIndex: 'operation',
    fixed: 'right',
    width: 150,
    align: 'center',
  },
];

const formList = ref([
  {
    type: 'input',
    value: 'code',
    label: '指标编码',
  },
  {
    type: 'input',
    value: 'name',
    label: '指标名称',
  },
  // {
  //   type: 'select',
  //   value: 'typeCode',
  //   label: '分类',
  //   attrs: {
  //     fieldNames: {
  //       label: 'typeName',
  //       value: 'typeCode',
  //     },
  //   },
  //   list: [],
  // },
]);

const state = ref({
  classifyArr: [],
  dataVersionsArr: [],
  tableLoading: false,
  sourceListArr: [],
  typeCode: 'target',
  previewVisible: false,
  editVisible: false,
  editLoading: false,
  submoduleName: 'particulars',
  paginations: {
    pageNo: 1,
    pageSize: 10,
    total: 0,
  },
  tableSource: [],
  targetQueryForm: {},
  checkTableArr: [],
});

const editObj = ref({});
const editRef = ref(null);
const addTargetModalRef = ref(null);

const addbtn = () => {
  // 打开弹窗
  addTargetModalRef.value.showModal('新增指标', null, props.allsuitable);
};

const delbtn = () => {
  Modal.confirm({
    title: '提示',
    icon: createVNode(ExclamationCircleFilled),
    content: '是否确认删除？',
    okText: '确认',
    cancelText: '取消',
    onOk() {
      http
        .post('/admin/rpt/metric/delete', {
          ids: state.value.checkTableArr,
        })
        .then(() => {
          message.success('操作成功！');
          getmetricPage();
          state.value.checkTableArr = [];
        });
    },
  });
};

const handleChange = () => {
  console.log('切换状态');
};

const handleTableChange = val => {
  state.value.paginations = val;
  getmetricPage();
};

const tableSelectChange = ids => {
  state.value.checkTableArr = ids;
};

const getmetricPage = () => {
  state.tableLoading = true;
  http
    .post('/admin/rpt/metric/page', {
      ...state.value.paginations,
      ...state.value.targetQueryForm,
      modelId: Session.get('modelId'),
    })
    .then(res => {
      state.value.tableSource = res.data.list;
      state.value.paginations.total = res.data.total;
    })
    .catch(e => {
      console.log('e', e);
    })
    .finally(() => {
      state.tableLoading = false;
    });
};

const previewVisible = data => {
  http.get('/admin/rpt/metric/details', { id: data.id }).then(res => {
    Object.assign(editObj.value, res.data);
    state.value.previewVisible = true;
  });
};

// 编辑指标
const compile = data => {
  http.get('/admin/rpt/metric/details', { id: data.id }).then(res => {
    // Object.assign(editObj.value, res.data);

    const resData = res.data || null;
    addTargetModalRef.value.showModal('编辑指标', resData, props.allsuitable);
  });
};

const targetqueryList = () => {
  state.value.paginations.pageNo = 1;
  getmetricPage();
};

const targetresetForm = () => {
  state.value.paginations.pageNo = 1;
  state.value.paginations.pageSize = 10;
  state.value.targetQueryForm = {};
  getmetricPage();
};

const getdataSourceList = () => {
  http.get('/admin/datasource/list').then(res => {
    state.value.sourceListArr = res.data;
  });
};

const editcancelForm = () => {
  editRef.value.resetFields();
  state.value.editVisible = false;
};

const edithandleOk = () => {
  editRef.value.validateFields().then(() => {
    http
      .post('/admin/rpt/metric/update', {
        ...editObj.value,
      })
      .then(res => {
        state.value.editVisible = false;
        getmetricPage();
      });
  });
};

const confirmBtn = (val, title) => {
  if (title === '新增指标') {
    http
      .post('/admin/rpt/metric/create', {
        ...val,
        modelId: Session.get('modelId'),
      })
      .then(() => {
        message.success('操作成功！');
        getmetricPage();
        addTargetModalRef.value.cancelForm();
      });
  } else {
    http
      .post('/admin/rpt/metric/update', {
        ...val,
        modelId: Session.get('modelId'),
        indicatorData: val.indicatorData === null ? '' : val.indicatorData,
        suitable: val.suitable === null ? '' : val.suitable,
      })
      .then(() => {
        message.success('操作成功！');
        getmetricPage();
        addTargetModalRef.value.cancelForm();
      });
  }
};

// type 1-指标 2-参数 3-数据
const getdataVersions = () => {
  http
    .get('/admin/rpt/source/dataVersions', {
      datasource: 'fsse_dw',
      type: 1,
    })
    .then(res => {
      state.value.dataVersionsArr = res.data;
    });
};

const lookAssociation = record => {
  console.log(record);
  if (record.indicatorData) {
    indicatorsLookModalRef.value.showModal(record);
  } else {
    message.warning('暂无关联题库指标');
  }
};

function convert(objStr) {
  if (!objStr) return '';
  const result = [];
  const obj = JSON.parse(objStr);

  // 遍历年级映射对象
  for (const gradeId of Object.keys(obj)) {
    let gradeName = '';
    const subjectNames = [];

    // 1. 查找年级名称
    for (const section of props.allsuitable) {
      const gradeInfo = section.gradeInfos.find(g => g.id === gradeId);
      if (gradeInfo) {
        gradeName = gradeInfo.name;

        // 2. 查找学科名称
        const subjectCodes = obj[gradeId];
        for (const code of subjectCodes) {
          const subject = section.subject.find(s => s.subjectCode === code);
          if (subject) subjectNames.push(subject.subjectName);
        }
        break;
      }
    }

    // 3. 拼接结果
    if (gradeName && subjectNames.length > 0) {
      result.push(`${gradeName}丨${subjectNames.join(',')}`);
    }
  }

  return result.join(' 、');
}

onMounted(() => {
  getmetricPage();

  getdataSourceList();
  // getdataVersions();
});
</script>

<style lang="less" scoped>
.particularsSearch {
  padding-left: 16px;
  padding-right: 16px;
}
.particularsTable {
  padding-left: 16px;
  padding-right: 16px;
  padding-bottom: 16px;
}

.queryform_class {
  display: flex;
  flex-wrap: wrap;
}

.tableListBtn {
  :deep(.ant-btn-link) {
    padding: unset;
  }
}

.parameterBox {
  padding-right: 16px;
  padding-left: 16px;
  padding-bottom: 24px;
}

.preName {
  font-weight: 400;
  font-size: 14px;
  color: #262626;
  line-height: 20px;

  font-style: normal;
  padding-bottom: 24px;
  .preName_item {
    font-weight: 400;
    font-size: 14px;
    color: #595959;
    line-height: 20px;

    font-style: normal;
  }
}
</style>
