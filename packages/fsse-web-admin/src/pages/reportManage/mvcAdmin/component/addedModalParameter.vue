<!-- 新增模型参数 -->
<template>
  <div>
    <a-modal v-model:open="state.open" title="新增模型参数" :confirmLoading="state.targetLoading" :maskClosable="false"
      :keyboard="false" :body-style="{ maxHeight: '660px', overflow: 'auto', padding: '24px' }"
      @cancel="cancelForm" @ok="handleOk" width="580px" okText="确认" cancelText="取消">
      <div>
        <!-- <div class="tipsBox">
          模型参数为每份报告中出现且内容不同的占位符，如：参与监测的学校“&#123;&#123;school_id&#125;&#125;”，每个学校名称都不同。
        </div> -->
        <a-form ref="targetRef" layout="vertical" :model="targetObj" name="targetObj" :label-col="{ span: 24 }"
          :wrapper-col="{ span: 24 }" autocomplete="off">
          <!-- <a-form-item label="数据源：" name="datasourceId" :rules="[
            {
              required: true,
              message: '请选择数据源',
            },
          ]">
            <a-select v-model:value="targetObj.datasourceId" placeholder="请选择" :options="state.sourceListArr"
              :showArrow="true" @change="datasourceChange" :field-names="{
                label: 'name',
                value: 'id',
              }">
            </a-select>
          </a-form-item>
          <a-form-item label="版本号：" name="versionCode" :rules="[
            {
              required: true,
              message: '请选择版本号',
            },
          ]">
            <a-select :disabled="!targetObj.datasourceId" v-model:value="targetObj.versionCode" placeholder="请选择"
              @change="versionCodeChange" :options="state.dataVersionsArr" :showArrow="true" :field-names="{
                label: 'name',
                value: 'code',
              }">
            </a-select>
          </a-form-item> -->

          <a-form-item label="参数编码：" name="datasourceId" mb24 :rules="[
            {
              required: true,
              message: '请输入指标编码',
            },
          ]">
            <a-input show-count :maxlength="20"></a-input>
          </a-form-item>
          <a-form-item label="指标名称：" name="datasourceId" mb24 :rules="[
            {
              required: true,
              message: '请输入指标名称',
            },
          ]">
            <a-input show-count :maxlength="20"></a-input>
          </a-form-item>
          <a-form-item name="datasourceId" layout="horizontal" :rules="[
            {
              required: false,
              message: '请设置关联题库指标',
            },
          ]">
            <template #label>
              关联题库指标： <a-button type="link" @click="openSetModal">设置</a-button>
            </template>
          </a-form-item>
          <a-form-item label="描述：" name="datasourceId" mb24 :rules="[
            {
              required: true,
              message: '请输入描述',
            },
          ]">
            <a-input></a-input>
          </a-form-item>
          <a-form-item help="说明：若为通用，则可不选择适用对象" label="适用对象：" name="datasourceId" mb24 :rules="[
            {
              required: false,
              message: '请选择适用对象',             
            },
          ]">
              <a-table :columns="columns" :data-source="targetObj.arr" :pagination="false" bordered>            
                <template #bodyCell="{ record, column, index }">
                  <a-form-item v-if="column.key=='grade'" :name="column.dataIndex"  :rules="[{ required: false, message: '请选择' }]"> 
                    <a-select v-model:value="record[column.dataIndex]" placeholder="请选择">
                      <a-select-option value="shanghai">Zone one</a-select-option>
                    </a-select>
                  </a-form-item>
                  <a-form-item v-if="column.key=='subject'" :name="column.dataIndex"  :rules="[{ required: false, message: '请选择' }]"> 
                    <div style="display: flex;align-items: center;">
                        <a-select v-model:value="record[column.dataIndex]" placeholder="请选择"  mode="multiple">
                        <a-select-option value="shanghai">Zone one</a-select-option>
                      </a-select>                  
                      <div  style="display: flex;align-items: center;margin-left: 8px;">
                        <PlusCircleOutlined style="padding: 0 8px;font-size: 18px;cursor: pointer;color: #11C685;"/>
                        <template v-if="index!=0">
                          <MinusCircleOutlined style="font-size: 18px;cursor: pointer;color: #F5222D;" />
                        </template>
                        <template v-else>
                          <span style="display: inline;width: 18px;"/>
                        </template>
                      </div>
                    </div>
                  </a-form-item>
                </template>
              </a-table>
          </a-form-item>
        </a-form>
      </div>
      <!-- <div class="targetTable">
        <div class="allCheck">
          <div>
            <a-checkbox v-model:checked="state.allCheck" @change="allCheckChange"
              :disabled="!targetObj.datasourceId">全选</a-checkbox>
          </div>
          <div>请选择模型参数，支持多选</div>
        </div>
        <a-table rowKey="code" :row-selection="{
          selectedRowKeys: state.selectedRowKeys,
          onChange: onSelectChange,
        }" :columns="targetListColumns" :pagination="false" :data-source="state.targetListData">
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'description'">
              <a-tooltip v-if="record.description.length > 15">
                <template #title>{{ record.description }}</template>
                <div class="ellipsis" style="width: 200px">
                  {{ record.description }}
                </div>
              </a-tooltip>
              <div v-else>
                {{ record.description }}
              </div>
            </template>
          </template></a-table>
      </div> -->
    </a-modal>

  </div>
</template>

<script setup>
import { reactive, onMounted, ref } from 'vue';
import { Session } from '@/utils/storage';

const columns = [
  { title: '适用年级', dataIndex: 'grade', key: 'grade',width:190 },
  { title: '适用学科', dataIndex: 'subject', key: 'subject' },
]
const route = useRoute();
const emit = defineEmits(['confirmBtn']);
const state = ref({
  open: false,
  targetVisible: false,
  allCheck: false,
  targetLoading: false,
  targetListSping: false,
  targetListData: [],
  sourceListArr: [],
  dataVersionsArr: [],
  selectedRowKeys: [],
  selectedRows: [],

  indicatorsVisible: false,
});
const targetRef = ref(null);
const targetObj = ref({
  arr:[{}]
});

function openSetModal() {
  state.value.indicatorsVisible = true;
}

const cancelForm = () => {
  targetObj.value.datasourceId = null;
  targetObj.value.versionCode = null;
  state.value.targetListData = [];
  state.value.selectedRowKeys = [];
  state.value.selectedRows = [];
  state.value.allCheck = false;
  targetRef.value.resetFields();
  state.value.open = false;
};

const handleOk = () => {
  targetRef.value.validate().then(() => {
    emit('confirmBtn', {
      datasourceId: targetObj.value.datasourceId,
      versionCode: targetObj.value.versionCode,
      selectedRows: state.value.selectedRows,
    });
  });
};

const targetListColumns = [
  {
    title: '参数编码',
    dataIndex: 'code',
    key: 'code',
  },
  {
    title: '参数名称',
    dataIndex: 'name',
    key: 'name',
  },
  {
    title: '描述',
    dataIndex: 'description',
    key: 'description',
  },
];

const getdataSourceList = () => {
  http.get('/admin/datasource/list').then(res => {
    state.value.sourceListArr = res.data;
  });
};

const datasourceChange = val => {
  console.log('[ val ] >', val);
};

const versionCodeChange = val => {
  state.value.selectedRowKeys = [];
  http
    .get('/admin/rpt/model/parameter/listParameters', {
      datasourceId: targetObj.value.datasourceId,
      versionCode: targetObj.value.versionCode,
      modelId: route.query.modelId || Session.get('modelId'),
    })
    .then(res => {
      if (res.data.length) {
        state.value.targetListData = res.data.filter(item => !item.selected);
      } else {
        state.value.targetListData = [];
      }
    })
    .catch(e => {
      state.value.targetListData = [];
      state.value.selectedRowKeys = [];
    });
};

const onSelectChange = (selectedRowKeys, selectedRows) => {
  state.value.selectedRowKeys = selectedRowKeys;
  state.value.selectedRows = selectedRows;
};

const allCheckChange = e => {
  if (e.target.checked) {
    state.value.selectedRowKeys = state.value.targetListData.map(
      item => item.code
    );
    state.value.selectedRows = state.value.targetListData;
  } else {
    state.value.selectedRowKeys = [];
    state.value.selectedRows = [];
  }
};

// type 1-指标 2-参数 3-数据
const getdataVersions = () => {
  http
    .get('/admin/rpt/source/dataVersions', { datasource: 'fsse_dw', type: 2 })
    .then(res => {
      state.value.dataVersionsArr = res.data;
    });
};

// 发请求 决定能不能打开 决定能不能用
const openModal = () => {
  http
    .get('/admin/rpt/model/details', {
      id: route.query.modelId || Session.get('modelId'),
    })
    .then(res => {
      state.value.open = true;
      const parameterVersionCode = res.data.parameterVersionCode;
      // type 1-指标 2-参数 3-数据
      http
        .get('/admin/rpt/source/dataVersions', {
          datasource: 'fsse_dw',
          type: 2,
        })
        .then(res => {
          const resultArr = res.data || [];
          if (parameterVersionCode) {
            state.value.dataVersionsArr = resultArr.map(item => {
              return {
                ...item,
                disabled: item.code !== parameterVersionCode,
              };
            });
          } else {
            state.value.dataVersionsArr = resultArr;
          }
        });
    });
};

getdataSourceList();
// getdataVersions()
defineExpose({ cancelForm, openModal });
</script>

<style lang="less" scoped>
.allCheck {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-bottom: 8px;
}

.tipsBox {
  font-weight: 400;
  font-size: 14px;
  color: #f58622;
  line-height: 20px;
  text-align: left;
  font-style: normal;
  padding-bottom: 12px;
}
</style>
