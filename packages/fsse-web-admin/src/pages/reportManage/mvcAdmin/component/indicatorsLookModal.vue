<template>
  <a-modal
    v-model:open="state.open"
    title="查看关联题库指标"
    :width="800"
    :footer="null"
    @cancel="handleCancel"
  >
    <div class="container-box">
      <a-spin :spinning="state.loadLoading">
        <div class="orgBox">
          机构: {{ state.indicatorData?.orgName || '-' }}
        </div>
        <div class="body">
          <div class="left">
            <a-row
              style="
                max-height: 500px;
                overflow-y: auto;
                padding-left: 16px;
                padding-right: 16px;
              "
            >
              <a-col
                :span="24"
                style="
                  padding: 8px 0;
                  display: flex;
                  align-items: center;
                  cursor: pointer;
                "
                v-for="(item, idx) in state.indicatorData.indicatorGroup"
                :key="item.id"
                @click="handleSelect(item, idx)"
              >
                <a-tooltip :title="item.name">
                  <span
                    :class="{ active: state.checked == idx }"
                    class="ellipsis"
                    style="
                      padding: 5px 12px;
                      display: inline-block;
                      max-width: 100%;
                    "
                    >{{ item.name }}</span
                  >
                </a-tooltip>
              </a-col>
            </a-row>
          </div>
          <div class="right" style="max-height: 465px; overflow-y: auto">
            <a-tree
              blockNode
              defaultExpandAll
              :selectable="false"
              :tree-data="state.treeData"
              v-model:expandedKeys="state.expandedKeys"
              :fieldNames="{ children: 'childs', title: 'name', key: 'id' }"
            >
            </a-tree>
          </div>
        </div>
      </a-spin>
    </div>
  </a-modal>
</template>

<script setup>
import { get } from 'lodash';

const state = reactive({
  loadLoading: false,
  checked: null,
  open: false,
  indicatorData: {},
  treeData: [],
});

const showModal = item => {
  state.treeData = [];
  state.checked = null;
  http
    .get('admin/rpt/metric/details', {
      id: item.id,
    })
    .then(res => {
      if (res.data.indicatorData) {
        state.indicatorData = JSON.parse(res.data.indicatorData);
      } else {
        state.indicatorData = {};
      }

      console.log(state.indicatorData);

      state.open = true;
    });

  state.open = true;
};

const getExpandedKeys = (data, keys = []) => {
  data.forEach(item => {
    keys.push(item.id);
    if (item.childs && item.childs.length > 0) {
      getExpandedKeys(item.childs, keys);
    }
  });
  return keys;
};

const handleSelect = (item, idx) => {
  state.checked = idx;
  state.treeData = item.indicator;
  // 默认展开全部节点
  state.expandedKeys = getExpandedKeys(item.indicator);
};

defineExpose({
  showModal,
});
</script>

<style lang="less" scoped>
.container-box {
  position: relative;
  padding: 16px 16px;

  .body {
    height: 100%;
    min-height: 287px;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    display: flex;

    .left {
      width: 250px;
      border-right: 1px solid #d9d9d9;
      .active {
        background-color: rgba(0, 183, 129, 0.1);
        color: #00b781;
        border-radius: 4px;
      }
    }

    .right {
      flex: 1;
      padding: 16px;
    }
  }
}

.orgBox {
  padding-bottom: 16px;
}

.right {
  :deep(.ant-tree .ant-tree-treenode) {
    padding: 12px 12px 12px 0;

    border-bottom: 1px solid #d9d9d9;
    &:first-child {
      border-top: 1px solid #d9d9d9;
    }
  }
  :deep(.ant-tree .ant-tree-switcher) {
    display: none;
  }
}
</style>
