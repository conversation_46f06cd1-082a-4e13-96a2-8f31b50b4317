<!-- 参数集的页面 -->
<!-- 参数集的页面 -->
<!-- 参数集的页面 -->
<template>
  <div class="particularsSearch">
    <!-- 查询 -->
    <searchForm
      v-model:formState="parameterQueryForm"
      :formList="formList"
      @submit="parameterqueryList"
      @reset="parameterresetForm"
    ></searchForm>
    <div class="btn-wrap" pb16 flex flex-justify-end>
      <a-button type="primary" @click="addbtn">新增</a-button>
      <a-button :disabled="state.checkTableArr.length === 0" @click="delbtn"
        >删除</a-button
      >
    </div>
  </div>
  <div class="particularsTable">
    <ETable
      hash="parameterPage_table"
      :loading="state.tableLoading"
      :columns="parameterColumns"
      :dataSource="state.tableSource"
      :total="+state.paginations.total"
      @paginationChange="handleTableChange"
      :current="state.paginations.pageNo"
      :row-selection="{
        selectedRowKeys: state.checkTableArr,
        onChange: tableSelectChange,
      }"
      :scroll="{ x: true }"
    >
      <template #correlation="{ record }">
        <a-button type="link" @click="openIndicatorsModal(record)"
          >关联</a-button
        >
      </template>
                 <template #suitable="{ record }">
           <span :title="convert(record.suitable)">
            {{convert(record.suitable) || '通用'}}
           </span>
          </template>
      <template #operation="{ record }">
        <a-button
          style="padding-right: 12px"
          type="link"
          class="btn-link-color"
          @click="compile(record)"
          >编辑</a-button
        >
      </template>
    </ETable>
  </div>
  <!--弹窗的组件-->
  <CustomModal ref="customModalRef" @confirmBtn="confirmBtn"></CustomModal>
</template>

<script setup name="parameterPageCom">
import { createVNode, onMounted } from 'vue';

import { Session } from '@/utils/storage';
import { message, Modal } from 'ant-design-vue';
import { ExclamationCircleFilled } from '@ant-design/icons-vue';

import CustomModal from './customModal.vue';

const props = defineProps({
  allsuitable: {
    type: Array,
    default: () => [],
  },
});

const state = ref({
  tableLoading: false,
  checkTableArr: [],
  paginations: {
    pageNo: 1,
    pageSize: 10,
    total: 0,
  },
  tableSource: [],
  targetQueryForm: {},
  openLook: false,
});

const parameterColumns = [
  {
    title: '参数编码',
    dataIndex: 'code',
    key: 'code',
  },
  {
    title: '参数名称',
    dataIndex: 'name',
    key: 'name',
  },
  {
    title: '描述',
    dataIndex: 'description',
    key: 'description',
    width: '400px',
  },
  {
    title: '适用对象',
    dataIndex: 'suitable',
    key: 'suitable',
  },
  {
    title: '创建人',
    dataIndex: 'createBy',
    key: 'createBy',
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    key: 'createTime',
  },
  {
    title: '操作',
    key: 'operation',
    dataIndex: 'operation',
    fixed: 'right',
  },
];

const formList = ref([
  {
    type: 'input',
    value: 'code',
    label: '参数编码',
  },
  {
    type: 'input',
    value: 'name',
    label: '参数名称',
  },
]);

const customModalRef = ref(null);
const parameterQueryForm = ref({});

const openIndicatorsModal = e => {
  console.log('查看关联', e);
  state.value.openLook = true;
};

const tableSelectChange = ids => {
  state.value.checkTableArr = ids;
};
// 获取分页
const getParameterPage = () => {
  http
    .post('/admin/rpt/model/custom-parameter/page', {
      ...state.value.paginations,
      ...parameterQueryForm.value,
      modelId: Session.get('modelId'),
    })
    .then(res => {
      state.value.tableSource = res.data.list;
      state.value.paginations.total = res.data.total;
    });
};

const handleTableChange = val => {
  state.value.paginations = val;
  getParameterPage();
};

const changeType = e => {
  state.value.paginations.pageNo = 1;
  state.value.paginations.pageSize = 10;
  parameterQueryForm.value = {};
  state.value.checkTableArr = [];
  getParameterPage();
};

const parameterqueryList = () => {
  state.value.paginations.pageNo = 1;
  getParameterPage();
};

const parameterresetForm = () => {
  changeType();
};

const addbtn = () => {
  customModalRef.value.showModal('add', null, props.allsuitable);
};

const compile = record => {
  customModalRef.value.showModal('edit', record, props.allsuitable);
};

const delbtn = () => {
  Modal.confirm({
    title: '提示',
    icon: createVNode(ExclamationCircleFilled),
    content: '是否确认删除？',
    okText: '确认',
    cancelText: '取消',
    onOk() {
      http
        .post('/admin/rpt/model/custom-parameter/delete', {
          ids: state.value.checkTableArr,
        })
        .then(() => {
          message.success('操作成功！');
          state.value.checkTableArr = [];
          changeType();
        });
    },
  });
};

const confirmBtn = (type, obj) => {
  if (type === 'add') {
    http
      .post('/admin/rpt/model/custom-parameter/create', {
        ...obj,
        modelId: Session.get('modelId'),
      })
      .then(() => {
        message.success('操作成功！');
        changeType();
      });
  } else {
    http
      .post('/admin/rpt/model/custom-parameter/update', {
        ...obj,
        modelId: Session.get('modelId'),
      })
      .then(() => {
        message.success('操作成功！');
        changeType();
      });
  }
};


function convert(objStr) {
  if (!objStr) return '';
  const result = [];
  const obj =  JSON.parse(objStr);
  
  // 遍历年级映射对象
  for (const gradeId of Object.keys(obj)) {
    let gradeName = '';
    const subjectNames = [];
    
    // 1. 查找年级名称
    for (const section of  props.allsuitable) {
      const gradeInfo = section.gradeInfos.find(g => g.id === gradeId);
      if (gradeInfo) {
        gradeName = gradeInfo.name;
        
        // 2. 查找学科名称
        const subjectCodes = obj[gradeId];
        for (const code of subjectCodes) {
          const subject = section.subject.find(s => s.subjectCode === code);
          if (subject) subjectNames.push(subject.subjectName);
        }
        break;
      }
    }
    
    // 3. 拼接结果
    if (gradeName && subjectNames.length > 0) {
      result.push(`${gradeName}丨${subjectNames.join(',')}`);
    }
  }
  
  return result.join(' 、');
}


onMounted(() => {
  getParameterPage();
});
</script>

<style lang="less" scoped>
.particularsSearch {
  padding-left: 16px;
  padding-right: 16px;
}
.particularsTable {
  padding-left: 16px;
  padding-right: 16px;
  padding-bottom: 16px;
}

.queryform_class {
  display: flex;
  flex-wrap: wrap;
}

.tableListBtn {
  :deep(.ant-btn-link) {
    padding: unset;
  }
}

.tipsBox {
  font-weight: 400;
  font-size: 14px;
  color: #f58622;
  line-height: 20px;
  text-align: left;
  font-style: normal;
  padding-bottom: 12px;
}
</style>
