# 指标设置模态框组件 (indicatorsSetModal.vue)

## 功能概述

这是一个用于关联题库指标的模态框组件，支持选择机构、指标类型，并通过树形结构选择具体的指标项。

## 主要特性

### 1. 数据结构保持
- ✅ 输出的数据结构保持完整的树形层级关系
- ✅ 只包含选中的节点及其必要的父节点路径
- ✅ 支持多级嵌套的指标结构

### 2. 树形组件功能
- ✅ 封装了 `IndicatorTree.vue` 组件
- ✅ 支持全选/半选状态的联动效果
- ✅ 收集全选和半选的节点数据
- ✅ 为每个指标分组维护独立的选中状态

### 3. 编辑回显功能
- ✅ 支持传入初始数据进行回显
- ✅ 自动设置机构、指标类型、分组选中状态
- ✅ 恢复树形组件的选中状态

### 4. 用户交互
- ✅ 左侧指标分组列表支持搜索
- ✅ 支持分组间切换时保存和恢复选中状态
- ✅ 右侧树形组件支持全选功能

## 使用方法

### 基本使用
```vue
<template>
  <IndicatorsSetModal
    ref="modalRef"
    @submit="handleSubmit"
  />
</template>

<script setup>
import IndicatorsSetModal from './indicatorsSetModal.vue';

const modalRef = ref(null);

// 打开模态框
const openModal = () => {
  modalRef.value?.showModal();
};

// 打开模态框并回显数据
const openModalWithData = (existingData) => {
  modalRef.value?.showModal(existingData);
};

// 处理提交数据
const handleSubmit = (data) => {
  console.log('收到的指标数据:', data);
};
</script>
```

## 数据结构

### 输入数据结构（用于回显）
```javascript
{
  orgId: "100040",
  indicatorType: "examination",
  indicatorGroup: [
    {
      id: "1872567273281687554",
      name: "指标a",
      indicator: [
        {
          id: "1621456386981855555",
          name: "通用体系大青蛙",
          childs: [
            {
              id: "162145643511982434",
              name: "二级指标1",
              childs: [
                {
                  id: "1914127585730781185",
                  name: "三级指标2"
                }
              ]
            },
            {
              id: "1890303801139675137",
              name: "二级指标3"
            }
          ]
        }
      ]
    }
  ]
}
```

### 输出数据结构
输出的数据结构与输入结构相同，保持完整的树形层级关系。

## API

### Props
无

### Events
- `submit(data)`: 当用户点击确定时触发，返回选中的指标数据

### Methods
- `showModal(initData?)`: 打开模态框，可选传入初始数据用于回显

## 组件文件

- `indicatorsSetModal.vue`: 主组件
- `IndicatorTree.vue`: 封装的树形组件
- `test-indicators.vue`: 测试组件

## 回显逻辑说明

### 问题描述
当使用带初始数据进行回显时，需要正确区分哪些节点应该显示为全选状态，哪些应该显示为半选状态。

### 解决方案
实现了 `calculateTreeCheckState` 函数来正确计算树形组件的选中状态：

1. **识别真正选中的节点**：从保存的树形数据中找出叶子节点（没有子节点的节点）
2. **计算父节点状态**：
   - 如果所有子节点都被选中 → 父节点全选
   - 如果部分子节点被选中 → 父节点半选
   - 如果没有子节点被选中 → 父节点未选中

### 示例
假设用户选中了"三级指标2"和"二级指标3"：
- **全选状态**：`["1914127585730781185", "1890303801139675137"]`（三级指标2、二级指标3）
- **半选状态**：`["1621456386981855555", "162145643511982434"]`（通用体系大青蛙、二级指标1）

## 注意事项

1. 组件依赖 `http` 全局对象进行API调用
2. 使用了 Ant Design Vue 的组件库
3. 支持 Vue 3 Composition API
4. 树形数据结构中使用 `children` 字段表示子节点
5. 输出数据结构中使用 `childs` 字段表示子节点
6. 回显时会在控制台输出调试信息，可用于验证计算结果
