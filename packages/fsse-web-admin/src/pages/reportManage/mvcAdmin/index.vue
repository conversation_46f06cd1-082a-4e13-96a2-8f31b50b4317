<!-- 模型管理的主页面 -->
<!-- 模型管理的主页面 -->
<template>
  <div class="dataManage" v-if="state.showMainPage">
    <ContHeader />
    <div p-16>
      <searchForm
        v-model:formState="query"
        :formList="formList"
        @submit="queryBySubmit"
        @reset="queryByReset"
        mb-20
      ></searchForm>

      <div class="btn-wrap" pb16 flex flex-justify-end>
        <a-button type="primary" @click="openModeling('新增模型')"
          >新增</a-button
        >
        <a-button
          danger
          @click="delModel"
          :disabled="!state.selectedRowKeys.length"
          >删除</a-button
        >
        <!-- <a-button @click="openDataOrigin">数据源管理</a-button> -->
        <a-button @click="openModelType">模型类型</a-button>
      </div>
      <div>
        <ETable
          hash="mvcAdminTableBox"
          :loading="page.loading"
          :columns="columns"
          :dataSource="page.list"
          :total="+page.total"
          @paginationChange="paginationChange"
          :current="query.pageNo"
          :row-selection="{
            selectedRowKeys: state.selectedRowKeys,
            onChange: onSelectChange,
          }"
        >
          <template #status="{ record }">
            <a-badge
              :color="getStatusObj(record.status).color"
              :text="getStatusObj(record.status).text"
            />
          </template>
          <template #description="{ record }">
            <span :title="record.description">{{
              record.description || '-'
            }}</span>
          </template>

          <template #suitable="{ record }">
            <span :title="convert(record.suitable)">
              {{ convert(record.suitable) || '通用' }}
            </span>
          </template>

          <template #operate="{ record }">
            <a-button
              danger
              v-if="record.status === 'enabled'"
              class="btn-link-color"
              type="link"
              @click="forbidden(record, 'disabled')"
              >禁用</a-button
            >
            <a-button
              v-if="record.status === 'disabled'"
              class="btn-link-color"
              type="link"
              @click="forbidden(record, 'enabled')"
              >启用</a-button
            >
            <a-button
              type="link"
              @click="openModeling('编辑模型', record)"
              class="btn-link-color"
              >编辑</a-button
            >
            <a-button
              type="link"
              @click="openParticulars(record)"
              class="btn-link-color"
              >详情</a-button
            >
          </template>
        </ETable>
      </div>
    </div>
  </div>
  <!-- 其他的组件 -->
  <div class="submodule" v-else>
    <!-- 数据源管理的页面 或者是详情的组件 -->
    <component
      ref="compntRef"
      :is="submodule[state.submoduleName]"
      :allsuitable="state.allsuitable"
      :modelId="state.modelId"
      @rollback="rollback"
    ></component>
  </div>

  <typeListModal ref="typeListModalRef" @submitAdd="getlistModelType" />
  <addModelingModal ref="addModelingModalRef" @submitAdd="getList" />
</template>

<script setup name="mvcAdminCom">
import { Modal } from 'ant-design-vue';
import { createVNode } from 'vue';
import { ExclamationCircleFilled } from '@ant-design/icons-vue';
import { Session } from '@/utils/storage';
import typeListModal from './component/typeListModal.vue';
import addModelingModal from './component/addModelingModal.vue';

function convert(objStr) {
  if (!objStr) return '';
  const result = [];
  const obj = JSON.parse(objStr);

  // 遍历年级映射对象
  for (const gradeId of Object.keys(obj)) {
    let gradeName = '';
    const subjectNames = [];

    // 1. 查找年级名称
    for (const section of state.allsuitable) {
      const gradeInfo = section.gradeInfos.find(g => g.id === gradeId);
      if (gradeInfo) {
        gradeName = gradeInfo.name;

        // 2. 查找学科名称
        const subjectCodes = obj[gradeId];
        for (const code of subjectCodes) {
          const subject = section.subject.find(s => s.subjectCode === code);
          if (subject) subjectNames.push(subject.subjectName);
        }
        break;
      }
    }

    // 3. 拼接结果
    if (gradeName && subjectNames.length > 0) {
      result.push(`${gradeName}丨${subjectNames.join(',')}`);
    }
  }

  return result.join(' 、');
}

const compntRef = ref(null);
// 加载页面的组件
const submodule = {
  particulars: defineAsyncComponent(
    () => import('./component/particulars.vue')
  ),
  dataOrigin: defineAsyncComponent(() => import('./component/dataOrigin.vue')),
};

const allOption = { id: null, name: '全部' };

const state = reactive({
  allsuitable: [],
  selectedRowKeys: [],
  showMainPage: true,
  modelId: '',
  submoduleName: 'particulars',
});

const typeListModalRef = ref(null);
const addModelingModalRef = ref(null);
let { query, page, getList, reset, paginationChange } = useList(
  '/admin/rpt/model/page'
);

// 显示状态
const statusObj = {
  disabled: {
    text: '已禁用',
    color: '#F5222D',
  },
  enabled: {
    text: '已启用',
    color: '#00C088',
  },
  default: {
    text: '-',
    color: '',
  }, // 默认值存储在 default 属性中
};

function getStatusObj(key) {
  return statusObj[key] || statusObj.default;
}

const formList = ref([
  {
    type: 'input',
    value: 'name',
    label: '模型名称',
  },
  {
    type: 'select',
    value: 'typeId',
    label: '模型类型',
    attrs: {
      fieldNames: {
        label: 'name',
        value: 'id',
      },
    },
    list: [],
  },
  {
    type: 'select',
    value: 'status',
    label: '状态',
    list: [
      {
        label: '全部',
        value: null,
      },
      {
        label: '已启用',
        value: 'enabled',
      },
      {
        label: '已禁用',
        value: 'disabled',
      },
    ],
  },
]);

const columns = [
  { title: '模型名称', dataIndex: 'name', key: 'name' },
  { title: '描述', dataIndex: 'description', key: 'description' },
  { title: '模型类型', dataIndex: 'typeName', key: 'typeName' },
  { title: '适用对象', dataIndex: 'suitable', key: 'suitable' },
  { title: '创建时间', dataIndex: 'createTime', key: 'createTime' },
  { title: '创建人', dataIndex: 'createBy', key: 'createBy' },
  { title: '状态', dataIndex: 'status', key: 'status', width: 100 },
  {
    title: '操作',
    dataIndex: 'operate',
    key: 'operate',
    fixed: 'right',
    width: 150,
  },
];

const onSelectChange = selectedRowKeys => {
  state.selectedRowKeys = selectedRowKeys;
};

const queryBySubmit = () => {
  getList();
};

const queryByReset = () => {
  reset();
};

const openModelType = () => {
  typeListModalRef.value.showModal();
};

const openModeling = (title, record) => {
  addModelingModalRef.value.showModal(title, record, state.allsuitable);
};

// 禁用启用的事件
const forbidden = (data, val) => {
  http
    .post('/admin/rpt/model/update', {
      id: data.id,
      status: val,
    })
    .then(res => {
      getList();
    });
};

// 获取模型类型列表
const getlistModelType = () => {
  http.post('/admin/rpt/model-type/list').then(res => {
    formList.value[1].list = [allOption, ...res.data];
  });
};

// const openDataOrigin = () => {
//   state.submoduleName = 'dataOrigin';
//   state.showMainPage = false;
// };

const openParticulars = data => {
  state.modelId = data.id;
  Session.set('modelId', data.id);
  state.submoduleName = 'particulars';
  state.showMainPage = false;
};

const rollback = val => {
  state.showMainPage = val;
};

const delModel = () => {
  Modal.confirm({
    title: '提示',
    cancelText: '取消',
    okText: '确认',
    icon: createVNode(ExclamationCircleFilled),
    content: '是否确认删除？',
    centered: false,
    async onOk() {
      await http.post('/admin/rpt/model/delete', {
        ids: state.selectedRowKeys,
      });
      YMessage.success('删除成功');
      state.selectedRowKeys = [];
      getList();
    },
    onCancel() {},
  });
};

const getListSectionSubject = () => {
  http.post('/admin/rpt/model/listSectionSubject').then(res => {
    state.allsuitable = res.data;
  });
};

onMounted(() => {
  getList();
  getlistModelType();
  getListSectionSubject();
});
</script>

<style lang="less" scoped>
.dataManage {
  height: 100%;
}
</style>
