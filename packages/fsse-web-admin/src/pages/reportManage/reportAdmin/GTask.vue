<template>
  <div>
    <a-modal
      v-model:open="state.open"
      width="569px"
      :closable="false"
      :maskClosable="false"
      :destroyOnClose="true"
      @cancel="cancel"
    >
      <div class="GTask">
        <header>
          <p>生成报告</p>
          <a-button type="link" class="btn-link" @click="cancel">
            <i class="iconfont icon-bjmbq-xx" :style="{ fontSize: '19px' }"></i>
          </a-button>
        </header>

        <div class="task_wrap">
          <p class="summary">
            合计{{
              state.details.itemsTotal || 0
            }}份报告，正在生成中，如需离开，可关闭窗口，系统将在后台自动生成报告
          </p>
          <ul class="task">
            <li v-for="item in state.details.jobItemList" :key="item.id">
              <p class="task_header">
                <span class="title">{{ item.entityName }}</span>
                <span :style="{ color: statusColor(item) }">{{
                  statusTitle(item)
                }}</span>
              </p>
              <a-progress
                :percent="percent(item)"
                size="small"
                trailColor="#EDEDED"
                :strokeColor="strokeColor(item)"
                :showInfo="false"
              />
            </li>
          </ul>
        </div>
      </div>
      <template #footer>
        <a-button @click="close" v-if="isComplete">关闭</a-button>
        <a-button @click="cancelTask" v-else>取消任务</a-button>
      </template>
    </a-modal>

    <Ball ref="ballRef" />
  </div>
</template>

<script setup>
import { createVNode } from 'vue';
import { message, Modal } from 'ant-design-vue';
import { ExclamationCircleFilled } from '@ant-design/icons-vue';
import Ball from './Ball.vue';

let timer = null;

// *********************
// Hooks Function
// *********************

const emit = defineEmits(['confirm']);

const ballRef = ref(null);

const state = reactive({
  open: false,
  details: {},
  record: {},
  id: '',
});

const statusTitle = computed(() => {
  return item => {
    if (item.status === 'succeeded') {
      return '已完成';
    } else if (item.status === 'pending') {
      return '等待中…';
    } else if (item.status === 'failed') {
      return `生成失败, ${item.errorMessage}`;
    } else if (item.status === 'cancelled') {
      return '已取消';
    } else {
      const percent = Math.floor(
        item.stepsTotal ? item.stepsFinished / item.stepsTotal : 0
      );
      return `生成中${percent * 100}%`;
    }
  };
});

const statusColor = computed(() => {
  return item => {
    if (item.status === 'succeeded') {
      return '#00B781';
    } else if (item.status === 'pending') {
      return '#8C8C8C';
    } else if (item.status === 'failed') {
      return '#F5222D';
    } else if (item.status === 'cancelled') {
      return '#666666';
    } else {
      return '#F5C322';
    }
  };
});

const percent = computed(() => {
  return item => {
    if (['succeeded', 'failed'].includes(item.status)) {
      return 100;
    } else if (['cancelled', 'pending'].includes(item.status)) {
      return 0;
    } else {
      const percent = Math.floor(
        item.stepsTotal ? item.stepsFinished / item.stepsTotal : 0
      );
      return percent;
    }
  };
});

const strokeColor = computed(() => {
  return item => {
    if (item.status === 'succeeded') {
      return '#00B781';
    } else if (item.status === 'pending') {
      return '#EDEDED';
    } else if (item.status === 'failed') {
      return '#F5222D';
    } else if (item.status === 'cancelled') {
      return '#EDEDED';
    } else {
      return '#77DABF';
    }
  };
});

const isComplete = computed(() => {
  return !['pending', 'running'].includes(state.details.status);
});

// *********************
// Default Function
// *********************

const getTaskDetails = async id => {
  const params = {
    id,
  };
  const { data } = await http.get('/admin/rpt/job/details', params);
  state.details = data || {};
  if (['pending', 'running'].includes(data.status)) {
    timer = setTimeout(() => {
      getTaskDetails(id);
    }, 1000);
  } else {
    // 生成报告完成，关闭弹框
    timer && clearTimeout(timer);
    state.open = false;
    emit('confirm', id);
  }
};

// *********************
// Life Event Function
// *********************

// *********************
// Service Function
// *********************

const cancel = evt => {
  ballRef.value.drop({
    x: evt.clientX,
    y: evt.clientY,
  });
  state.open = false;
};

const close = () => {
  state.open = false;
};

const cancelTask = async () => {
  const params = {
    id: state.id,
  };
  const res = await http.get('/admin/rpt/job/cancel', params);
  message.success(res.message);
  state.open = false;
};

const cancelTaskModal = () => {
  Modal.confirm({
    title: '提示',
    icon: createVNode(ExclamationCircleFilled),
    content: `取消将导致当前任务下所有已生成报告被清除，是否确认取消？`,
    okText: '确 定',
    cancelText: '取消',
    onCancel() {},
    onOk() {
      return cancelTask();
    },
  });
};

// *********************
// Watch Function
// *********************

watch(
  () => state.open,
  val => {
    if (!val) {
      timer && clearTimeout(timer);
    }
  }
);

// *********************
// DefineExpose Function
// *********************

const showModal = id => {
  state.id = id;
  state.open = true;
  getTaskDetails(id);
};

defineExpose({ showModal });
</script>

<style lang="less" scoped>
.GTask {
  margin: -24px;
  padding-left: 24px;
  padding-right: 24px;

  header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 54px;
    padding: 0 16px 0 24px;
    border-bottom: 1px solid #d9d9d9;

    p {
      font-weight: 500;
      font-size: 16px;
      color: #000000;
    }
    .btn-link {
      color: #bfbfbf;
    }
  }

  .task_wrap {
    padding: 16px 24px;
    height: 560px;
    overflow-y: auto;
    .summary {
      font-weight: 400;
      font-size: 14px;
      color: #f58622;
      margin-bottom: 16px;
    }

    .task {
      li {
        margin-bottom: 24px;
      }
      .task_header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-weight: 500;
        font-size: 14px;
        color: #000000;
      }
    }
  }
}

.close_dot {
  position: absolute;
  top: 0;
  right: 0;
}
</style>
