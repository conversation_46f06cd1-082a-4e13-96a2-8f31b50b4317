<template>
  <div>
    <a-modal v-model:open="state.open" width="1150px" :title="state.details.name" :maskClosable="false" :destroyOnClose="true">
      <div class="MTask">
        <div class="task_wrap">
          <p class="summary">合计{{ state.details.itemsTotal || 0 }}份报告，正在生成中，如需离开，可关闭窗口，系统将在后台自动生成报告</p>
          <ul class="task">
            <li v-for="item in state.details.jobItemList" :key="item.id">
              <p class="task_header">
                <span class="title">{{ item.entityName }}</span> <span :style="{ color: statusColor(item) }">{{ statusTitle(item) }}</span>
              </p>
              <a-progress :percent="percent(item)" size="small" trailColor="#EDEDED" :strokeColor="strokeColor(item)" :showInfo="false" />
            </li>
          </ul>
        </div>
      </div>
      <template #footer>
        <a-button @click="cancel" v-if="isComplete">关 闭</a-button>
        <a-button @click="cancelTaskModal" v-else>取 消</a-button>
      </template>
    </a-modal>
  </div>
</template>

<script setup>
import { createVNode } from 'vue'
import { message, Modal } from 'ant-design-vue'
import { ExclamationCircleFilled } from '@ant-design/icons-vue'

let timer = null

// *********************
// Hooks Function
// *********************

const emit = defineEmits(['cancel'])

const state = reactive({
  open: false,
  details: {},
  record: {}
})

const statusTitle = computed(() => {
  return (item) => {
    if (item.status === 'succeeded') {
      return '已完成'
    } else if (item.status === 'pending') {
      return '等待中…'
    } else if (item.status === 'failed') {
      return `生成失败, ${item.errorMessage}`
    } else if (item.status === 'cancelled') {
      return '已取消'
    } else {
      const percent = Math.floor(item.stepsTotal ? item.stepsFinished / item.stepsTotal : 0)
      return `生成中${percent * 100}%`
    }
  }
})

const statusColor = computed(() => {
  return (item) => {
    if (item.status === 'succeeded') {
      return '#00B781'
    } else if (item.status === 'pending') {
      return '#8C8C8C'
    } else if (item.status === 'failed') {
      return '#F5222D'
    } else if (item.status === 'cancelled') {
      return '#666666'
    } else {
      return '#F5C322'
    }
  }
})

const percent = computed(() => {
  return (item) => {
    if (['succeeded', 'failed'].includes(item.status)) {
      return 100
    } else if (['cancelled', 'pending'].includes(item.status)) {
      return 0
    } else {
      const percent = Math.floor(item.stepsTotal ? item.stepsFinished / item.stepsTotal : 0)
      return percent
    }
  }
})

const strokeColor = computed(() => {
  return (item) => {
    if (item.status === 'succeeded') {
      return '#00B781'
    } else if (item.status === 'pending') {
      return '#EDEDED'
    } else if (item.status === 'failed') {
      return '#F5222D'
    } else if (item.status === 'cancelled') {
      return '#EDEDED'
    } else {
      return '#77DABF'
    }
  }
})

const isComplete = computed(() => {
  return !['pending', 'running'].includes(state.details.status)
})

// *********************
// Default Function
// *********************
const getTaskDetails = async (id) => {
  const params = {
    id
  }
  const { data } = await http.get('/admin/rpt/job/details', { params })
  state.details = data || {}
  if (['pending', 'running'].includes(data.status)) {
    timer = setTimeout(() => {
      getTaskDetails(id)
    }, 1000)
  }
}

// *********************
// Life Event Function
// *********************

// *********************
// Service Function
// *********************

const cancel = () => {
  state.open = false
  emit('cancel')
}

const cancelTask = async () => {
  const params = {
    id: state.record.id
  }
  const res = await http.get('/admin/rpt/job/cancel', { params })
  message.success(res.message)
  cancel()
}

const cancelTaskModal = () => {
  Modal.confirm({
    title: '提示',
    icon: createVNode(ExclamationCircleFilled),
    content: `取消将导致当前任务下所有已生成报告被清除，是否确认取消？`,
    okText: '确 定',
    cancelText: '取消',
    onCancel() {},
    onOk() {
      return cancelTask()
    }
  })
}

// *********************
// Watch Function
// *********************

watch(
  () => state.open,
  (val) => {
    if (!val) {
      timer && clearTimeout(timer)
    }
  }
)

// *********************
// DefineExpose Function
// *********************

const showModal = (record) => {
  state.record = record
  state.open = true
  getTaskDetails(record.id)
}

defineExpose({ showModal })
</script>

<style lang="less" scoped>
.MTask {
  margin: -24px;
  padding-left: 24px;
  padding-right: 24px;
  .task_wrap {
    padding: 16px 24px;
    height: 560px;
    overflow-y: auto;
    .summary {
      font-weight: 400;
      font-size: 14px;
      color: #f58622;
      margin-bottom: 16px;
    }

    .task {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      grid-column-gap: 60px;
      grid-row-gap: 24px;

      .task_header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-weight: 500;
        font-size: 14px;
        color: #000000;
        .title {
          text-wrap: nowrap;
          margin-right: 20px;
        }
      }
    }
  }
}
</style>
