<template>
  <div>
    <a-modal
      v-model:open="state.open"
      width="569px"
      :body-style="{ padding: '24px' }"
      :closable="false"
      :maskClosable="false"
      :destroyOnClose="true"
      @cancel="cancel"
    >
      <a-spin :spinning="state.spinning">
        <div class="GResult">
          <div class="header">
            <img src="@/assets/images/correct.png" alt="" />
            <p>报告已经全部生成</p>
          </div>

          <div class="error_wrap">
            <p class="title">以下指标存在错误</p>
            <p class="content">{{ errorMessage }}</p>
            <p class="tips">提示:如需修改，请返回修改报告模版</p>
          </div>
        </div>
      </a-spin>

      <template #footer>
        <div class="footer">
          <a-button type="primary" @click="confirm">确定</a-button>
        </div>
      </template>
    </a-modal>
  </div>
</template>

<script setup>
import { createVNode } from 'vue';
import { message, Modal } from 'ant-design-vue';
import { ExclamationCircleFilled } from '@ant-design/icons-vue';

let timer = null;

// *********************
// Hooks Function
// *********************

const state = reactive({
  open: false,
  details: {},
  spinning: false,
});

const errorMessage = computed(() => {
  const jobItemList = state.details.jobItemList || [];
  return (
    jobItemList
      .filter(item => item.errorMessage)
      .map(item => `{{ ${item.errorMessage} }}`)
      .join('、') || '无异常信息'
  );
});

// *********************
// Default Function
// *********************

const getTaskDetails = async id => {
  try {
    state.spinning = true;
    const params = {
      id,
    };
    const { data } = await http.get('/admin/rpt/job/details', params);
    state.details = data || {};
  } finally {
    state.spinning = false;
  }
};

// *********************
// Life Event Function
// *********************

// *********************
// Service Function
// *********************

const confirm = () => {
  state.open = false;
};

// *********************
// Watch Function
// *********************

// *********************
// DefineExpose Function
// *********************

const showModal = id => {
  state.open = true;
  getTaskDetails(id);
};

defineExpose({ showModal });
</script>

<style lang="less" scoped>
.GResult {
  .header {
    display: flex;
    align-items: center;
    justify-content: center;
    img {
      width: 25px;
    }
    p {
      font-weight: 600;
      font-size: 16px;
      color: #000000;
      margin-left: 10px;
    }
  }

  .error_wrap {
    padding: 20px 0;
    .title {
      font-weight: 400;
      font-size: 14px;
      color: rgba(140, 140, 140, 0.88);
    }

    .content {
      margin: 20px 0;
    }

    .tips {
      font-weight: 400;
      font-size: 14px;
      color: #f58622;
    }
  }
}

.footer {
  display: flex;
  justify-content: center;
}
</style>
