<template>
  <div class="templatePage">
    <ContHeader />
    <div class="templatePageContent">
      <searchForm
        v-model:formState="state.queryForm"
        :formList="formList"
        @submit="queryList"
        @reset="resetForm"
        mb-20
      ></searchForm>

      <div class="btn-wrap" pb16 flex flex-justify-end>
        <a-button type="primary" @click="addbtn">新增</a-button>
      </div>

      <div class="cardList">
        <div
          class="cardItem"
          v-for="(item, index) in state.cardListArr"
          :key="index"
          @click="jumpEditor(item, 'edit')"
        >
          <div class="leftBox">
            <div class="imgBox"><img src="@/assets/images/temp.png" /></div>
            <div class="titleBox">
              <div class="titlecon">{{ item.title }}</div>
              <div class="infoBox">
                <span class="createBy">创建人：{{ item.createBy }}</span>
                <span class="createTime">创建时间：{{ item.createTime }}</span>
                <span class="modelName">绑定模型：{{ item.modelName }}</span>
              </div>
            </div>
          </div>
          <div class="rightBox">
            <div class="preview" @click.stop="jumpEditor(item, 'preview')">
              <i class="iconfont icon-bggl-ck"></i> 预览
            </div>
            <div class="produce" @click.stop="openGenerate(item)">
              <i class="iconfont icon-bggl-scbg"></i> 生成报告
            </div>
            <div @click.stop="delModel(item)" class="delete">
              <i class="iconfont icon-bggl-sc"></i> 删除
            </div>
          </div>
        </div>
      </div>
      <div
        style="margin: 16px 16px 0 0; text-align: right"
        v-if="state.total > 10"
      >
        <a-pagination
          v-model:current="state.pageNo"
          v-model:page-size="state.pageSize"
          :page-size-options="pageSizeOptions"
          show-size-changer
          :show-total="total => `共 ${total} 条`"
          :total="state.total"
          show-quick-jumper
          @change="handleChange"
        />
      </div>
    </div>

    <!-- 新增弹窗 -->
    <a-modal
      v-model:open="state.newlyVisible"
      title="新增"
      :body-style="{ maxHeight: '660px', overflow: 'auto', padding: '24px' }"
      :confirmLoading="state.newlyLoading"
      :maskClosable="false"
      :keyboard="false"
      centered
      @cancel="newlycancelForm"
      @ok="newlyhandleOk"
      width="428px"
      okText="确认"
      cancelText="取消"
    >
      <div>
        <a-form
          ref="newlyRef"
          layout="vertical"
          :model="newlyObj"
          name="newlyObj"
          :label-col="{ span: 24 }"
          :wrapper-col="{ span: 24 }"
          autocomplete="off"
        >
          <a-form-item
            label="数据模型："
            name="modelId"
            mb24
            :rules="[
              {
                required: true,
                message: '请选择数据模型',
              },
            ]"
          >
            <a-select
              v-model:value="newlyObj.modelId"
              placeholder="请选择"
              :options="state.databaseListArr"
              :showArrow="true"
              :field-names="{
                label: 'name',
                value: 'id',
              }"
            >
            </a-select>
          </a-form-item>

          <a-form-item
            mb24
            label="模版名称："
            name="title"
            :rules="[
              {
                required: true,
                message: '请输入模版名称',
              },
            ]"
          >
            <a-input
              placeholder="请输入"
              showCount
              :maxlength="15"
              v-model:value.trim="newlyObj.title"
            />
          </a-form-item>
          <a-form-item
            label="对比维度："
            name="dimNums"
            :rules="[
              {
                required: true,
                message: '请选择对比维度',
              },
            ]"
          >
            <a-select
              v-model:value="newlyObj.dimNums"
              :options="dimNumsOptions"
            ></a-select>
          </a-form-item>
        </a-form>
      </div>
    </a-modal>

    <Generate ref="generateRef" @confirm="openGTask" />
    <GTask ref="gTaskRef" @confirm="openGResult" />
    <GResult ref="gResultRef" />
  </div>
</template>

<script setup name="templatePage">
import { createVNode } from 'vue';
import { ExclamationCircleFilled } from '@ant-design/icons-vue';

import { message, Modal } from 'ant-design-vue';
import Generate from '@/pages/reportManage/reportAdmin/Generate.vue';
import GTask from '@/pages/reportManage/reportAdmin/GTask.vue';
import GResult from '@/pages/reportManage/reportAdmin/GResult.vue';

const pageSizeOptions = shallowRef(['10', '20', '30', '40', '50']);

const dimNumsOptions = Array.from({ length: 4 }).map((i, index) => ({
  label: index + 1,
  value: index + 1,
}));

const router = useRouter();

const state = ref({
  pageNo: 1,
  pageSize: 10,
  total: 0,
  queryForm: {},
  cardListArr: [],
  databaseListArr: [],
  newlyVisible: false,
  newlyLoading: false,
});

const generateRef = ref(null);
const gTaskRef = ref(null);
const gResultRef = ref(null);

const newlyRef = ref(null);
const newlyObj = ref({
  dimNums: 3,
});

const formList = ref([
  {
    type: 'input',
    value: 'title',
    label: '模版名称',
  },
  {
    type: 'select',
    value: 'modelId',
    label: '数据模型',
    attrs: {
      fieldNames: {
        label: 'name',
        value: 'id',
      },
    },
    list: [],
  },
]);

const gettemplatepage = () => {
  http
    .post('/admin/rpt/template/page', {
      pageNo: state.value.pageNo,
      pageSize: state.value.pageSize,
      ...state.value.queryForm,
    })
    .then(res => {
      state.value.cardListArr = res.data.list || [];
      state.value.total = res.data.total;
    });
};

const getmodelList = () => {
  http.post('/admin/rpt/model/list', { status: 'enabled' }).then(res => {
    console.log('res', res);
    state.value.databaseListArr = res.data;
    formList.value[1].list = res.data;
  });
};

const addbtn = () => {
  state.value.newlyVisible = true;
};

const newlycancelForm = () => {
  newlyRef.value.resetFields();
  state.value.newlyVisible = false;
};

const newlyhandleOk = () => {
  newlyRef.value.validateFields().then(() => {
    http
      .post('/admin/rpt/template/create', {
        ...newlyObj.value,
      })
      .then(res => {
        // 跳转到编辑器界面
        jumpEditor(
          {
            modelId: newlyObj.value.modelId,
            fileId: res.data.fileId,
            id: res.data.id,
          },
          'edit'
        );

        message.success('操作成功！');
        newlycancelForm();
        gettemplatepage();
      });
  });
};

const queryList = () => {
  state.value.pageNo = 1;
  gettemplatepage();
};

const resetForm = () => {
  state.value.pageNo = 1;
  state.value.pageSize = 10;
  state.value.queryForm = {};
  gettemplatepage();
};

const delModel = data => {
  Modal.confirm({
    title: '提示',
    icon: createVNode(ExclamationCircleFilled),
    content: '是否确认删除？',
    okText: '确认',
    cancelText: '取消',
    onOk() {
      http
        .get('/admin/rpt/template/delete', {
          id: data.id,
        })
        .then(() => {
          message.success('操作成功！');
          state.value.pageNo = 1;
          gettemplatepage();
        });
    },
  });
};

const handleChange = (page, pageSize) => {
  state.value.pageNo = page;
  state.value.pageSize = pageSize;
  gettemplatepage();
};

const jumpEditor = (record, mode) => {
  // 跳转到编辑器界面
  const { href } = router.resolve({
    name: 'reportManageEditor',
    query: {
      id: record.id,
      modelId: record.modelId,
      fileId: record.fileId,
      mode,
    },
  });
  window.open(href, '_blank');
};

const openGenerate = record => {
  generateRef.value.showModal(record);
};

const openGTask = id => {
  gTaskRef.value.showModal(id);
};

const openGResult = id => {
  gResultRef.value.showModal(id);
};

gettemplatepage();
getmodelList();
</script>

<style lang="less" scoped>
.templatePage {
  height: 100%;
  overflow-y: auto;
  .templatePageContent {
    padding: 16px;
  }
}
.queryform_class {
  display: flex;
  flex-wrap: wrap;
}

.cardList {
  .cardItem {
    cursor: pointer;
    height: 84px;
    background: #ffffff;
    box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.1);
    border-radius: 4px;
    margin-bottom: 16px;
    padding: 16px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .leftBox {
      display: flex;
      align-items: center;
      .imgBox {
        width: 50px;
        height: 50px;
        background: #ebfaf5;
        border-radius: 4px;
        display: flex;
        align-items: center;
        justify-content: center;
        img {
          width: 37px;
          height: 39px;
        }
      }
      .titleBox {
        padding-left: 10px;
        .titlecon {
          font-weight: 500;
          font-size: 14px;
          color: #000000;
          line-height: 20px;
          text-align: left;
          font-style: normal;
          padding-bottom: 10px;
        }
        .infoBox {
          font-weight: 400;
          font-size: 14px;
          color: #595959;
          line-height: 20px;
          text-align: left;
          font-style: normal;
          .createBy {
            display: inline-block;
            width: 200px;
          }
          .createTime {
            display: inline-block;
            width: 230px;
          }
          .modelName {
            padding-left: 50px;
          }
        }
      }
    }
    .rightBox {
      display: flex;
      align-items: center;
      .preview,
      .produce {
        padding-right: 20px;
        color: #262626;
        font-size: 14px;
        cursor: pointer;
        &:hover {
          color: #00b781;
        }
      }
      .delete {
        color: #262626;
        font-size: 14px;
        cursor: pointer;
        &:hover {
          color: #f5222d;
        }
      }
    }
  }
}

.cardItem {
  &:hover {
    background: #f7fffc;
    box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.1);
  }
}
</style>
