<template>
  <div class="json-editor-test">
    <h2>CodeMirror JSON 编辑器测试</h2>
    
    <div class="test-section">
      <h3>测试 JSON 校验功能</h3>
      <p>请在下面的编辑器中输入错误的 JSON 格式，应该会在行号旁显示错误标记：</p>
      
      <CodeMirrorJsonEditor
        ref="jsonEditorRef"
        v-model="testJson"
        :height="'300px'"
        :title="'JSON 校验测试'"
        :show-toolbar="true"
        @change="handleJsonChange"
        @validate="handleJsonValidate"
      />
      
      <div class="test-info">
        <h4>测试说明：</h4>
        <ul>
          <li>输入正确的 JSON：<code>{"name": "test", "value": 123}</code></li>
          <li>输入错误的 JSON：<code>{"name": "test", "value": 123,}</code> (多余的逗号)</li>
          <li>输入错误的 JSON：<code>{"name": "test" "value": 123}</code> (缺少逗号)</li>
          <li>输入错误的 JSON：<code>{"name": "test", "value": abc}</code> (字符串未加引号)</li>
        </ul>
      </div>
      
      <div class="validation-result" v-if="validationResult">
        <h4>验证结果：</h4>
        <pre>{{ JSON.stringify(validationResult, null, 2) }}</pre>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import CodeMirrorJsonEditor from '@/components/common/CodeMirrorJsonEditor/index.vue';

// 响应式数据
const jsonEditorRef = ref(null);
const testJson = ref(`{
  "name": "test",
  "value": 123,
  "array": [1, 2, 3],
  "nested": {
    "key": "value"
  }
}`);
const validationResult = ref(null);

// 方法
const handleJsonChange = (value) => {
  console.log('JSON 内容变化:', value);
};

const handleJsonValidate = (result) => {
  console.log('验证结果:', result);
  validationResult.value = result;
};
</script>

<style lang="less" scoped>
.json-editor-test {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;

  h2 {
    color: #1890ff;
    margin-bottom: 20px;
  }

  .test-section {
    margin-bottom: 30px;

    h3 {
      color: #262626;
      margin-bottom: 10px;
    }

    p {
      color: #666;
      margin-bottom: 15px;
    }
  }

  .test-info {
    margin-top: 20px;
    padding: 15px;
    background: #f6f8fa;
    border-radius: 6px;

    h4 {
      margin-bottom: 10px;
      color: #262626;
    }

    ul {
      margin: 0;
      padding-left: 20px;

      li {
        margin-bottom: 5px;
        color: #666;

        code {
          background: #f1f3f4;
          padding: 2px 6px;
          border-radius: 3px;
          font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
          font-size: 12px;
        }
      }
    }
  }

  .validation-result {
    margin-top: 20px;
    padding: 15px;
    background: #fff2f0;
    border: 1px solid #ffccc7;
    border-radius: 6px;

    h4 {
      margin-bottom: 10px;
      color: #cf1322;
    }

    pre {
      margin: 0;
      font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
      font-size: 12px;
      color: #262626;
      background: #fafafa;
      padding: 10px;
      border-radius: 4px;
      overflow-x: auto;
    }
  }
}
</style>
