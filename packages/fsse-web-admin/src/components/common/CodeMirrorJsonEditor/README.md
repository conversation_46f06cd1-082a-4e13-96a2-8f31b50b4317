# CodeMirrorJsonEditor 组件

## 概述

CodeMirrorJsonEditor 是一个基于 `codemirror-editor-vue3` 的 Vue3 JSON 编辑器组件，用于替换原有的基于 `vue3-json-editor` 的 JsonEditor 组件。该组件提供了更好的性能和更丰富的功能。

## 特性

- ✅ **Vue3 兼容**: 专为 Vue3 设计，完全兼容 Composition API
- ✅ **CodeMirror 5**: 基于成熟稳定的 CodeMirror 5 编辑器
- ✅ **JSON 语法高亮**: 内置 JSON 语法高亮支持
- ✅ **实时校验**: 使用 jsonlint-mod 进行实时 JSON 格式校验
- ✅ **行号显示**: 显示行号，便于定位错误
- ✅ **JSON 格式化**: 内置格式化功能
- ✅ **工具栏**: 可选的工具栏，包含格式化、验证、复制等功能
- ✅ **错误提示**: 实时显示 JSON 语法错误
- ✅ **主题支持**: 支持多种 CodeMirror 主题
- ✅ **轻量级**: 相比 Monaco Editor 更轻量，避免与 WPS SDK 冲突

## 基础用法

```vue
<template>
  <CodeMirrorJsonEditor
    v-model="jsonData"
    :height="'300px'"
    title="JSON配置"
    @change="handleChange"
    @validate="handleValidate"
  />
</template>

<script setup>
import { ref } from 'vue';
import CodeMirrorJsonEditor from '@/components/common/CodeMirrorJsonEditor/index.vue';

const jsonData = ref({
  name: "示例",
  value: 123
});

const handleChange = (value) => {
  console.log('JSON内容变化:', value);
};

const handleValidate = (result) => {
  console.log('验证结果:', result);
};
</script>
```

## Props

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `modelValue` | `String \| Object` | `''` | 双向绑定的JSON数据 |
| `height` | `String` | `'300px'` | 编辑器高度 |
| `showToolbar` | `Boolean` | `true` | 是否显示工具栏 |
| `title` | `String` | `''` | 编辑器标题 |
| `readonly` | `Boolean` | `false` | 是否只读 |
| `theme` | `String` | `'default'` | CodeMirror 主题 |

## Events

| 事件名 | 参数 | 说明 |
|--------|------|------|
| `update:modelValue` | `value` | 双向绑定更新事件 |
| `change` | `value` | 内容变化事件 |
| `error` | `hasError` | 错误状态事件 |
| `validate` | `{valid, error}` | 验证结果事件 |

## 方法

通过 `ref` 可以调用以下方法：

| 方法名 | 参数 | 返回值 | 说明 |
|--------|------|--------|------|
| `getCodeValue()` | - | `String` | 获取JSON字符串值 |
| `setCodeValue(value)` | `String` | - | 设置JSON字符串值 |
| `formatJson()` | - | - | 格式化JSON |
| `validateJson()` | - | - | 验证JSON格式 |
| `copyJson()` | - | - | 复制JSON到剪贴板 |

## 使用示例

### 1. 基础编辑器

```vue
<CodeMirrorJsonEditor
  v-model="config"
  :height="'200px'"
  title="基础配置"
/>
```

### 2. 只读模式

```vue
<CodeMirrorJsonEditor
  v-model="readonlyData"
  :readonly="true"
  :show-toolbar="false"
  title="只读查看"
/>
```

### 3. 自定义主题

```vue
<CodeMirrorJsonEditor
  v-model="data"
  :theme="'monokai'"
  :height="'400px'"
  title="深色主题编辑器"
/>
```

### 4. 完整配置

```vue
<CodeMirrorJsonEditor
  ref="editorRef"
  v-model="jsonConfig"
  :height="'350px'"
  :show-toolbar="true"
  :theme="'default'"
  title="完整配置编辑器"
  @change="handleJsonChange"
  @validate="handleJsonValidate"
  @error="handleJsonError"
/>
```

## 迁移指南

### 从 JsonEditor 迁移

如果你之前使用的是 JsonEditor 组件，可以按以下步骤迁移：

1. **替换导入**:
```javascript
// 旧的
import JsonEditor from '@/components/common/JsonEditor/index.vue';

// 新的
import CodeMirrorJsonEditor from '@/components/common/CodeMirrorJsonEditor/index.vue';
```

2. **更新模板**:
```vue
<!-- 旧的 -->
<JsonEditor
  ref="jsonEditorRef"
  v-model="configCode"
  :height="'200px'"
  :mode="'code'"
  title="JSON配置"
/>

<!-- 新的 -->
<CodeMirrorJsonEditor
  ref="jsonEditorRef"
  v-model="configCode"
  :height="'200px'"
  title="JSON配置"
/>
```

3. **方法调用保持兼容**:
```javascript
// 获取值的方法保持兼容
const value = jsonEditorRef.value?.getCodeValue();

// 设置值的方法保持兼容
jsonEditorRef.value?.setCodeValue(newValue);
```

## 注意事项

1. **依赖要求**: 需要安装 `codemirror-editor-vue3`、`codemirror@^5` 和 `jsonlint-mod` 依赖
2. **数据格式**: 支持字符串和对象两种数据格式的双向绑定
3. **实时校验**: 内置 JSON 格式校验，实时反馈错误信息
4. **性能优化**: 基于 CodeMirror 5，性能优秀且稳定

## 故障排除

### 常见问题

1. **组件不显示**: 检查是否正确安装了所有依赖包
2. **校验不工作**: 确保 `jsonlint-mod` 依赖已正确安装
3. **样式异常**: 确保父容器有明确的高度设置
4. **数据不更新**: 检查 `v-model` 绑定是否正确

### 调试技巧

```javascript
// 监听所有事件进行调试
<CodeMirrorJsonEditor
  @change="(val) => console.log('change:', val)"
  @validate="(res) => console.log('validate:', res)"
  @error="(err) => console.log('error:', err)"
/>
```
