<template>
  <div class="codemirror-json-editor-wrapper" :style="{ height: props.height }">
    <!-- 工具栏 -->
    <div class="json-editor-toolbar" v-if="showToolbar">
      <div class="toolbar-left">
        <span class="editor-title">{{ title || 'JSON编辑器' }}</span>
      </div>
      <div class="toolbar-right">
        <a-button
          type="text"
          size="small"
          @click="formatJson"
          :disabled="!canFormat"
        >
          <template #icon>
            <FormatPainterOutlined />
          </template>
          格式化
        </a-button>
        <a-button type="text" size="small" @click="validateJson">
          <template #icon>
            <CheckCircleOutlined />
          </template>
          验证
        </a-button>
        <a-button
          type="text"
          size="small"
          @click="copyJson"
          :disabled="!jsonValue"
        >
          <template #icon>
            <CopyOutlined />
          </template>
          复制
        </a-button>
      </div>
    </div>

    <!-- CodeMirror编辑器主体 -->
    <div class="codemirror-editor-container" :style="{ height: containerHeight }">
      <Codemirror
        ref="cmRef"
        v-model:value="jsonValue"
        :options="cmOptions"
        border
        :height="containerHeight"
        width="100%"
        @change="handleJsonChange"
        @input="onInput"
        @ready="onReady"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, onUnmounted } from 'vue';
import { message } from 'ant-design-vue';
import Codemirror from 'codemirror-editor-vue3';
import {
  FormatPainterOutlined,
  CheckCircleOutlined,
  CopyOutlined,
} from '@ant-design/icons-vue';

// 导入 CodeMirror 的 JSON 模式和 lint 功能
import 'codemirror/mode/javascript/javascript.js';
import 'codemirror/addon/lint/lint.js';
import 'codemirror/addon/lint/lint.css';
import 'codemirror/addon/lint/json-lint.js';

// 定义props
const props = defineProps({
  // 双向绑定的JSON数据
  modelValue: {
    type: [String, Object],
    default: '',
  },
  // 编辑器高度
  height: {
    type: String,
    default: '300px',
  },
  // 是否显示工具栏
  showToolbar: {
    type: Boolean,
    default: true,
  },
  // 编辑器标题
  title: {
    type: String,
    default: '',
  },
  // 是否只读
  readonly: {
    type: Boolean,
    default: false,
  },
  // 主题
  theme: {
    type: String,
    default: 'default',
  },
});

// 定义事件
const emit = defineEmits(['update:modelValue', 'change', 'error', 'validate']);

// 响应式数据
const cmRef = ref(null);
const jsonValue = ref('');
const validationError = ref('');
const isValidJson = ref(true);

// 计算属性
const containerHeight = computed(() => {
  // 如果父组件传入的是百分比或者其他单位，直接使用
  if (props.height.includes('%') || props.height === '100%') {
    return props.height;
  }

  const toolbarHeight = props.showToolbar ? 40 : 0;
  const totalHeight = parseInt(props.height);
  return `${totalHeight - toolbarHeight}px`;
});

const canFormat = computed(() => {
  return jsonValue.value && isValidJson.value;
});

// CodeMirror 配置选项
const cmOptions = computed(() => ({
  mode: 'application/json',
  theme: props.theme,
  lineNumbers: true,
  lineWrapping: true,
  readOnly: props.readonly,
  gutters: ["CodeMirror-lint-markers"],
  lint: true,
  tabSize: 2,
  indentUnit: 2,
  autoCloseBrackets: true,
  matchBrackets: true,
  foldGutter: true,
}));

// 监听modelValue变化
watch(
  () => props.modelValue,
  newValue => {
    if (newValue !== jsonValue.value) {
      if (typeof newValue === 'object' && newValue !== null) {
        jsonValue.value = JSON.stringify(newValue, null, 2);
      } else {
        jsonValue.value = newValue || '';
      }
    }
  },
  { immediate: true }
);

// 监听jsonValue变化
watch(
  jsonValue,
  newValue => {
    emit('update:modelValue', newValue);
    validateJsonContent(newValue);
  },
  { deep: true }
);

// 方法
const handleJsonChange = (value, cm) => {
  jsonValue.value = value;
  emit('change', value);
};

const onInput = (value) => {
  // 输入时的处理
};

const onReady = (cm) => {
  // 编辑器准备就绪时的处理
  cm.focus();
};

const validateJsonContent = (value) => {
  try {
    if (typeof value === 'string' && value.trim()) {
      JSON.parse(value);
    }
    isValidJson.value = true;
    validationError.value = '';
    emit('validate', { valid: true, error: null });
  } catch (error) {
    isValidJson.value = false;
    validationError.value = `JSON格式错误: ${error.message}`;
    emit('validate', { valid: false, error: error.message });
  }
};

const formatJson = () => {
  try {
    if (typeof jsonValue.value === 'string') {
      const parsed = JSON.parse(jsonValue.value);
      jsonValue.value = JSON.stringify(parsed, null, 2);
    } else {
      jsonValue.value = JSON.stringify(jsonValue.value, null, 2);
    }
    message.success('JSON格式化成功');
  } catch (error) {
    message.error('JSON格式化失败: ' + error.message);
  }
};

const validateJson = () => {
  validateJsonContent(jsonValue.value);
  if (isValidJson.value) {
    message.success('JSON格式验证通过');
  } else {
    message.error(validationError.value);
  }
};

const copyJson = async () => {
  try {
    const textToCopy =
      typeof jsonValue.value === 'string'
        ? jsonValue.value
        : JSON.stringify(jsonValue.value, null, 2);

    await navigator.clipboard.writeText(textToCopy);
    message.success('JSON内容已复制到剪贴板');
  } catch (error) {
    message.error('复制失败: ' + error.message);
  }
};

// 获取JSON值的方法（兼容原JsonEditor接口）
const getCodeValue = () => {
  return typeof jsonValue.value === 'string'
    ? jsonValue.value
    : JSON.stringify(jsonValue.value);
};

// 设置JSON值的方法（兼容原JsonEditor接口）
const setCodeValue = (value) => {
  jsonValue.value = value;
};

// 生命周期钩子
onMounted(() => {
  // 组件挂载后的处理
});

onUnmounted(() => {
  // 组件卸载时的清理
  if (cmRef.value) {
    cmRef.value.destroy && cmRef.value.destroy();
  }
});

// 暴露方法给父组件
defineExpose({
  getCodeValue,
  setCodeValue,
  formatJson,
  validateJson,
  copyJson,
  cmRef,
});
</script>

<style lang="less" scoped>
.codemirror-json-editor-wrapper {
  width: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background: #fff;
  border: 1px solid #d9d9d9;
  border-radius: 6px;

  .json-editor-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 40px;
    padding: 0 12px;
    background: #fafafa;
    border-bottom: 1px solid #f0f0f0;
    border-radius: 6px 6px 0 0;

    .toolbar-left {
      .editor-title {
        font-size: 14px;
        font-weight: 500;
        color: #262626;
      }
    }

    .toolbar-right {
      display: flex;
      gap: 4px;
    }
  }

  .codemirror-editor-container {
    position: relative;
    flex: 1;
    width: 100%;
    overflow: hidden;

    :deep(.codemirror-container) {
      height: 100% !important;

      .CodeMirror {
        height: 100% !important;
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        font-size: 13px;
        line-height: 1.5;
        border: none;
        border-radius: 0 0 6px 6px;

        .CodeMirror-scroll {
          min-height: 100%;
        }

        .CodeMirror-gutters {
          background: #fafafa;
          border-right: 1px solid #f0f0f0;
        }

        .CodeMirror-linenumber {
          color: #999;
          padding: 0 8px;
        }

        .CodeMirror-lint-marker-error {
          background: #ff4d4f;
          border-radius: 50%;
          color: #fff;
          cursor: pointer;
        }

        .CodeMirror-lint-marker-warning {
          background: #faad14;
          border-radius: 50%;
          color: #fff;
          cursor: pointer;
        }
      }
    }
  }
}

// 全局样式，用于 CodeMirror lint 提示
:global(.CodeMirror-lint-tooltip) {
  background: #fff;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  color: #262626;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  font-size: 12px;
  max-width: 300px;
  padding: 8px 12px;
  z-index: 1000;
}
</style>