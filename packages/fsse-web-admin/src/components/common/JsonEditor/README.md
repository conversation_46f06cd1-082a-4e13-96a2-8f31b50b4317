# JsonEditor 组件

## 概述

JsonEditor 是一个基于 `vue3-json-editor` 的 Vue3 JSON 编辑器组件，用于替换原有的 Monaco Editor 基础的 CodeEditor 组件，避免与 WPS SDK 的冲突。

## 特性

- ✅ **Vue3 兼容**: 专为 Vue3 设计，完全兼容 Composition API
- ✅ **多种模式**: 支持代码模式、树形模式、查看模式等
- ✅ **行号显示**: 在代码模式下显示行号
- ✅ **JSON 格式化**: 内置格式化功能
- ✅ **实时验证**: 实时 JSON 格式验证
- ✅ **工具栏**: 可选的工具栏，包含格式化、验证、复制等功能
- ✅ **状态栏**: 显示验证状态、行数、字符数等信息
- ✅ **轻量级**: 不依赖 Monaco Editor，避免与 WPS SDK 冲突

## 基础用法

```vue
<template>
  <JsonEditor
    v-model="jsonData"
    :height="'300px'"
    title="JSON配置"
    @change="handleChange"
    @validate="handleValidate"
  />
</template>

<script setup>
import { ref } from 'vue';
import JsonEditor from '@/components/common/JsonEditor/index.vue';

const jsonData = ref({
  name: "示例",
  value: 123
});

const handleChange = (value) => {
  console.log('JSON变化:', value);
};

const handleValidate = (result) => {
  if (!result.valid) {
    console.error('JSON格式错误:', result.error);
  }
};
</script>
```

## Props

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `modelValue` | `String \| Object` | `''` | 双向绑定的JSON数据 |
| `height` | `String` | `'300px'` | 编辑器高度 |
| `mode` | `String` | `'code'` | 编辑器模式：`tree`、`view`、`form`、`code`、`text` |
| `showToolbar` | `Boolean` | `true` | 是否显示工具栏 |
| `showStatus` | `Boolean` | `true` | 是否显示状态栏 |
| `title` | `String` | `''` | 编辑器标题 |
| `expandedOnStart` | `Boolean` | `true` | 是否初始展开 |
| `language` | `String` | `'zh'` | 界面语言 |
| `readonly` | `Boolean` | `false` | 是否只读 |

## Events

| 事件名 | 参数 | 说明 |
|--------|------|------|
| `update:modelValue` | `value` | 双向绑定更新事件 |
| `change` | `value` | 内容变化事件 |
| `error` | `hasError` | 错误状态事件 |
| `validate` | `{valid, error}` | 验证结果事件 |

## 方法

通过 `ref` 可以调用以下方法：

| 方法名 | 参数 | 返回值 | 说明 |
|--------|------|--------|------|
| `getCodeValue()` | - | `String` | 获取JSON字符串值 |
| `setCodeValue(value)` | `String` | - | 设置JSON字符串值 |
| `formatJson()` | - | - | 格式化JSON |
| `validateJson()` | - | - | 验证JSON格式 |
| `copyJson()` | - | - | 复制JSON到剪贴板 |

## 使用示例

### 1. 基础编辑器

```vue
<JsonEditor
  v-model="config"
  :height="'200px'"
  title="基础配置"
/>
```

### 2. 树形模式

```vue
<JsonEditor
  v-model="data"
  :mode="'tree'"
  :height="'400px'"
  title="树形编辑器"
/>
```

### 3. 只读模式

```vue
<JsonEditor
  v-model="readonlyData"
  :readonly="true"
  :mode="'view'"
  :show-toolbar="false"
  title="只读查看"
/>
```

### 4. 完整配置

```vue
<JsonEditor
  ref="editorRef"
  v-model="jsonConfig"
  :height="'350px'"
  :mode="'code'"
  :show-toolbar="true"
  :show-status="true"
  :expanded-on-start="true"
  title="完整配置编辑器"
  @change="handleJsonChange"
  @validate="handleJsonValidate"
  @error="handleJsonError"
/>
```

## 迁移指南

### 从 CodeEditor 迁移

如果你之前使用的是 CodeEditor 组件，可以按以下步骤迁移：

1. **替换导入**:
```javascript
// 旧的
import CodeEditor from '@/components/common/CodeEditor/index.vue';

// 新的
import JsonEditor from '@/components/common/JsonEditor/index.vue';
```

2. **更新模板**:
```vue
<!-- 旧的 -->
<CodeEditor
  ref="codeEditorRef"
  v-model:editCode="configCode"
  :height="'200px'"
  :options="{
    language: 'json',
    theme: 'vs',
  }"
/>

<!-- 新的 -->
<JsonEditor
  ref="jsonEditorRef"
  v-model="configCode"
  :height="'200px'"
  :mode="'code'"
  title="JSON配置"
/>
```

3. **更新方法调用**:
```javascript
// 获取值的方法保持兼容
const value = jsonEditorRef.value?.getCodeValue();

// 设置值的方法保持兼容
jsonEditorRef.value?.setCodeValue(newValue);
```

## 注意事项

1. **WPS SDK 兼容性**: 此组件不使用 Monaco Editor，完全避免与 WPS SDK 的冲突
2. **数据格式**: 支持字符串和对象两种数据格式的双向绑定
3. **验证功能**: 内置 JSON 格式验证，实时反馈错误信息
4. **性能优化**: 相比 Monaco Editor 更轻量，加载速度更快

## 故障排除

### 常见问题

1. **组件不显示**: 检查是否正确导入 `vue3-json-editor` 依赖
2. **样式异常**: 确保父容器有明确的高度设置
3. **数据不更新**: 检查 `v-model` 绑定是否正确

### 调试技巧

```javascript
// 监听所有事件进行调试
<JsonEditor
  @change="(val) => console.log('change:', val)"
  @validate="(res) => console.log('validate:', res)"
  @error="(err) => console.log('error:', err)"
/>
```
