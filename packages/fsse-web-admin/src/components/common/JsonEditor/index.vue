<template>
  <div class="json-editor-wrapper" :style="{ height: props.height }">
    <!-- 工具栏 -->
    <div class="json-editor-toolbar" v-if="showToolbar">
      <div class="toolbar-left">
        <span class="editor-title">{{ title || 'JSON编辑器' }}</span>
      </div>
      <div class="toolbar-right">
        <a-button
          type="text"
          size="small"
          @click="formatJson"
          :disabled="!canFormat"
        >
          <template #icon>
            <FormatPainterOutlined />
          </template>
          格式化
        </a-button>
        <a-button type="text" size="small" @click="validateJson">
          <template #icon>
            <CheckCircleOutlined />
          </template>
          验证
        </a-button>
        <a-button
          type="text"
          size="small"
          @click="copyJson"
          :disabled="!jsonValue"
        >
          <template #icon>
            <CopyOutlined />
          </template>
          复制
        </a-button>
      </div>
    </div>

    <!-- JSON编辑器主体 -->
    <div class="json-editor-container" :style="{ height: containerHeight }">
      <Vue3JsonEditor
        ref="jsonEditorRef"
        v-model="jsonValue"
        :show-btns="false"
        :expandedOnStart="expandedOnStart"
        :mode="editorMode"
        :lang="language"
        :style="{ width: '100%', height: '100%' }"
        @json-change="handleJsonChange"
        @has-error="handleError"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue';
import { message } from 'ant-design-vue';
import { Vue3JsonEditor } from 'vue3-json-editor';
import {
  FormatPainterOutlined,
  CheckCircleOutlined,
  CopyOutlined,
} from '@ant-design/icons-vue';

// 定义props
const props = defineProps({
  // 双向绑定的JSON数据
  modelValue: {
    type: [String, Object],
    default: '',
  },
  // 编辑器高度
  height: {
    type: String,
    default: '300px',
  },
  // 编辑器模式: tree, view, form, code, text
  mode: {
    type: String,
    default: 'code',
    validator: value =>
      ['tree', 'view', 'form', 'code', 'text'].includes(value),
  },
  // 是否显示工具栏
  showToolbar: {
    type: Boolean,
    default: true,
  },

  // 编辑器标题
  title: {
    type: String,
    default: '',
  },
  // 是否初始展开
  expandedOnStart: {
    type: Boolean,
    default: true,
  },
  // 语言
  language: {
    type: String,
    default: 'zh',
  },
  // 是否只读
  readonly: {
    type: Boolean,
    default: false,
  },
});

// 定义事件
const emit = defineEmits(['update:modelValue', 'change', 'error', 'validate']);

// 响应式数据
const jsonEditorRef = ref(null);
const jsonValue = ref('');
const validationError = ref('');
const isValidJson = ref(true);

// 计算属性
const containerHeight = computed(() => {
  // 如果父组件传入的是百分比或者其他单位，直接使用
  if (props.height.includes('%') || props.height === '100%') {
    return props.height;
  }

  const toolbarHeight = props.showToolbar ? 40 : 0;
  const totalHeight = parseInt(props.height);
  return `${totalHeight - toolbarHeight}px`;
});

const editorMode = computed(() => {
  return props.readonly ? 'view' : props.mode;
});

const canFormat = computed(() => {
  return jsonValue.value && isValidJson.value && props.mode === 'code';
});

// 监听modelValue变化
watch(
  () => props.modelValue,
  newValue => {
    if (newValue !== jsonValue.value) {
      jsonValue.value = newValue;
    }
  },
  { immediate: true }
);

// 监听jsonValue变化
watch(
  jsonValue,
  newValue => {
    emit('update:modelValue', newValue);
    validateJsonContent(newValue);
  },
  { deep: true }
);

// 方法
const handleJsonChange = value => {
  jsonValue.value = value;
  emit('change', value);
};

const handleError = hasError => {
  if (hasError) {
    isValidJson.value = false;
    emit('error', hasError);
  } else {
    isValidJson.value = true;
    validationError.value = '';
  }
};

const validateJsonContent = value => {
  try {
    if (typeof value === 'string' && value.trim()) {
      JSON.parse(value);
    }
    isValidJson.value = true;
    validationError.value = '';
    emit('validate', { valid: true, error: null });
  } catch (error) {
    isValidJson.value = false;
    validationError.value = `JSON格式错误: ${error.message}`;
    emit('validate', { valid: false, error: error.message });
  }
};

const formatJson = () => {
  try {
    if (typeof jsonValue.value === 'string') {
      const parsed = JSON.parse(jsonValue.value);
      jsonValue.value = JSON.stringify(parsed, null, 2);
    } else {
      jsonValue.value = JSON.stringify(jsonValue.value, null, 2);
    }
    message.success('JSON格式化成功');
  } catch (error) {
    message.error('JSON格式化失败: ' + error.message);
  }
};

const validateJson = () => {
  validateJsonContent(jsonValue.value);
  if (isValidJson.value) {
    message.success('JSON格式验证通过');
  } else {
    message.error(validationError.value);
  }
};

const copyJson = async () => {
  try {
    const textToCopy =
      typeof jsonValue.value === 'string'
        ? jsonValue.value
        : JSON.stringify(jsonValue.value, null, 2);

    await navigator.clipboard.writeText(textToCopy);
    message.success('JSON内容已复制到剪贴板');
  } catch (error) {
    message.error('复制失败: ' + error.message);
  }
};

// 获取JSON值的方法（兼容原CodeEditor接口）
const getCodeValue = () => {
  return typeof jsonValue.value === 'string'
    ? jsonValue.value
    : JSON.stringify(jsonValue.value);
};

// 设置JSON值的方法（兼容原CodeEditor接口）
const setCodeValue = value => {
  jsonValue.value = value;
};

// 暴露方法给父组件
defineExpose({
  getCodeValue,
  setCodeValue,
  formatJson,
  validateJson,
  copyJson,
});
</script>

<style lang="less" scoped>
.json-editor-wrapper {
  width: 100%;
  display: flex;
  flex-direction: column;

  overflow: hidden;
  background: #fff;

  .json-editor-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 40px;
    padding: 0 12px;
    background: #fafafa;
    border-bottom: 1px solid #f0f0f0;

    .toolbar-left {
      .editor-title {
        font-size: 14px;
        font-weight: 500;
        color: #262626;
      }
    }

    .toolbar-right {
      display: flex;
      gap: 4px;
    }
  }

  .json-editor-container {
    position: relative;
    flex: 1;
    width: 100%;
    overflow: hidden;

    :deep(.jsoneditor-vue) {
      height: 100% !important;
    }

    :deep(.vue3-json-editor) {
      width: 100% !important;
      height: 100% !important;
    }

    :deep(.jsoneditor) {
      width: 100% !important;
      height: 100% !important;
      border: none;

      .jsoneditor-tree {
        background: #fff;
      }

      .jsoneditor-code {
        background: #fff;
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        font-size: 13px;
        line-height: 1.5;
      }

      .jsoneditor-outer {
        width: 100% !important;
        height: 100% !important;
      }

      .jsoneditor-main {
        width: 100% !important;
        height: 100% !important;
      }
    }
  }
}
</style>
