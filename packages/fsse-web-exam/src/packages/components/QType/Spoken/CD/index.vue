<template>
  <div class="CD">
    <!-- 情景对话的题目内容区域 v-html渲染 -->
    <div :style="quesContentFontStyles" v-html="config.quesContent"></div>
    <audioPlay :config="config" />

    <!-- 情景对话的对话cds的区域 -->
    <div class="CDS">
      <div class="subListItem" v-for="(item, index) in config.subQues">
        <div>{{ index + 1 }}.Question：{{ item.title }}</div>
        <div class="answerBox">Answer:</div>
      </div>
    </div>
    <!-- 引入答题组件 -->
    <!-- 因为有些题 获取的任务数组从不同节点获取的 taskList还是外部传入吧 -->
    <StartSpokenEvaluate
      :config="config"
      :taskList="taskList"
      :defaultDuration="duration"
    ></StartSpokenEvaluate>
  </div>
</template>

<script setup>
import audioPlay from '@/packages/global/audioPlay.vue';
import StartSpokenEvaluate from '@/packages/global/startSpokenEvaluate.vue';
const props = defineProps({
  config: {
    type: Object,
    default: () => {},
  },
  topProperties: {
    type: Object,
    default: () => {},
  },
});

const taskList = computed(() => {
  const taskArr = props.config.subQues.map((item, index) => {
    return {
      taskName: item.title,
      taskId: item.quesCode,
    };
  });
  return taskArr;
});

const duration = computed(() => {
  if (!!props.config.quesContent) {
    return 45;
  } else {
    return 20;
  }
});


const quesContentFontStyles = computed(() => {
  const styleMap = props.topProperties?.quesContentFontStyles || {};
  return {
    ...styleMap,
    fontSize: (styleMap?.fontSize || 18) + 'px',
  };
});

</script>

<style scoped>
.CDS {
  padding-top: 16px;
}

.subListItem {
  padding-bottom: 24px;
}

.answerBox {
  padding-top: 12px;
  padding-left: 24px;
}
</style>
