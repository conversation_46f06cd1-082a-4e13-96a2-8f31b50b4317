<!-- 单词 -->
<template>
  <div class="WO">
    <!-- 音频 -->
    <audioPlay :config="config" />
    <SpokenDragOptionBox :config="config" :optionTool="optionTool">
      <template #middle="{ record, index }">
        <p
          class="char-title no-select"
          :style="optionFontStyles"
          v-if="!config.quesProperties.hideNumbers"
        >
          {{ index + 1 }}.
        </p>
        <!-- 设置学生需要读的单词 -->
        <RichText
          @textBlur="(e, value) => textBlur(e, value, record)"
          :style="optionFontStyles"
          v-model="record.title"
        />
      </template>
      <template #bottom="{ record, index }">
        <div v-if="record.errorMessage" class="errorbox">
          {{ record.errorMessage }}
        </div>
      </template>
    </SpokenDragOptionBox>

    <ToolBar :config="config" :leftList="leftList" :rightList="rightList">
      <template #addSubQues>
        <a-button type="link" class="btn-link-color" @click="addSubQuesBtn">
          <div flex flex-items-center>
            <i class="iconfont icon-tianjia1" mr-2></i>
            <span>新增单词</span>
          </div>
        </a-button>
      </template>
      <!-- 右侧 -->
      <template #hideNumbers>
        <a-checkbox v-model:checked="config.quesProperties.hideNumbers">
          <p class="hide-numbers">隐藏题目序号</p>
        </a-checkbox>
      </template>
    </ToolBar>
  </div>
</template>

<script setup>
import ToolBar from '@/packages/global/ToolBar.vue';
import SpokenDragOptionBox from '@/packages/global/SpokenDragOptionBox.vue';
import RichText from '@/packages/global/RichText.vue';
import audioPlay from '@/packages/global/audioPlay.vue';
import WOSConfigClass from './WOSconfig.js';
const editorStore = useEditorStore();

// 底部左侧工具栏
const leftList = [
  { key: 'addSubQues', name: '新增单词' },
  { key: '_audio' },
  { key: '_spokenAnswer' },
  { key: '_indicators' },
];

// 底部右侧工具栏
const rightList = [{ key: '_maxRow' }, { key: 'hideNumbers' }];

const addSubQuesBtn = () => {
  // 新增单词子题
  const subConfig = new WOSConfigClass();
  props.config.subQues.push(subConfig);
};

const optionFontStyles = computed(() => {
  const styleMap = editorStore.getExamSetting.optionFontStyles;
  return {
    ...styleMap,
    fontSize: styleMap.fontSize + 'px' || '18px',
  };
});

const props = defineProps({
  config: {
    type: Object,
    default: () => {},
  },
});

// 选项工具栏
const optionTool = [{ key: '_delete' }, { key: '_image' }];

// 这里要做单词的校验
const textBlur = (e, value, record) => {
  // 清除HTML标签，获取纯文本内容
  const plainText = value.replace(/<[^>]*>/g, '').trim();
  record.textTitle = plainText;
  // 如果内容为空，不进行校验
  if (!plainText) {
    record.errorMessage = '';
    return;
  }

  // 定义合法字符的正则表达式
  // 只允许英文字母和特定符号（点号、连字符、上单引号）
  // 单词分词只支持tab键、enter换行键、空格键。

  const validRegex = /^[a-zA-Z.\-'\t\n\r ]*$/;

  // 定义不允许的字符的正则表达式
  // 不允许问号、感叹号、分号、冒号、逗号以及特定字符（括号和方括号）
  const invalidChars = /[\?\!\;\:\,\(\)\[\]]/;

  // 校验文本
  if (!validRegex.test(plainText)) {
    // 检查是否包含不允许的字符
    if (invalidChars.test(plainText)) {
      // 找出所有不允许的字符
      const foundInvalidChars = [
        ...new Set(plainText.match(/[\?\!\;\:\,\(\)\[\]]/g)),
      ];
      record.errorMessage = `不允许使用以下字符: ${foundInvalidChars.join(' ')}`;
    } else {
      // 包含其他非法字符
      record.errorMessage = "只允许输入英文字母和特定符号（. - '）";
    }
  } else {
    // 校验通过，清除错误信息
    record.errorMessage = '';
    record.textTitle = plainText;
  }
};
</script>

<style lang="less" scoped>
.score-wrap {
  display: flex;
  align-items: center;
  .title {
    font-weight: 400;
    font-size: 14px;
    color: #8c8c8c;
    white-space: nowrap;
  }

  :deep(.ant-input) {
    width: 62px;
    padding: 0;
    height: 26px;
    background: #ffffff;
    border-radius: 4px;
    border: 1px solid #d9d9d9;
    font-weight: 400;
    font-size: 14px;
    color: #ffaa00;
    transition: none;
    text-align: center;
  }
}

.errorbox {
  padding-left: 20px;
  color: #f5222d;
  font-size: 12px;
  padding-top: 4px;
}
</style>
