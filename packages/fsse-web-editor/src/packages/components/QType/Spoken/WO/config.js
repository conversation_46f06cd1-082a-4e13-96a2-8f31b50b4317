import cloneDeep from 'lodash/cloneDeep';
import {
  PublicConfigClass,
  PublicOptionClass,
} from '@/packages/public/publicConfig.js';
import { WOConfig } from './index.js';
import WOSConfigClass from './WOSconfig.js';


// 单词中每一个对话都是一个小题,也就是说WO的题只有大题和小题 没有选项这种东西,新增单词也是新增1个小题,大题的答案解析和指标与所有小题共享
const getSubQues = () => {
  return Array.from({ length: 1 }, (_, index) => new WOSConfigClass());
};


export default class Config extends PublicConfigClass {
  quesTypeCode = WOConfig.key;
  // 当前的config信息
  config = cloneDeep(WOConfig);

  // 单词子题 WOS 默认先初始化1个题 后面自己一个对话一个题的push
  subQues = getSubQues();


  // 题目别名
  quesTypeAlias = WOConfig.title;

  constructor(op = {}) {
    super();
    // 由实例化是才决定需要需改的内容
    Object.assign(this, op);
  }
}
