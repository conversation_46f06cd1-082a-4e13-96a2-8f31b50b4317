<template>
  <div class="SE">
    <!-- 音频 -->
    <audioPlay :config="config" />
    <SpokenDragOptionBox :config="config" :optionTool="optionTool">
      <template #middle="{ record, index }">
        <p
          class="char-title no-select"
          :style="optionFontStyles"
          v-if="!config.quesProperties.hideNumbers"
        >
          {{ index + 1 }}.
        </p>
        <!-- 设置学生需要朗读的英文句子 -->
        <RichText
          @textBlur="(e, value) => textBlur(e, value, record)"
          :style="optionFontStyles"
          v-model="record.title"
        />
      </template>
      <template #bottom="{ record, index }">
        <div v-if="record.errorMessage" class="errorbox">
          {{ record.errorMessage }}
        </div>
      </template>
    </SpokenDragOptionBox>
    <ToolBar :config="config" :leftList="leftList" :rightList="rightList">
      <template #addSubQues>
        <a-button type="link" class="btn-link-color" @click="addSubQuesBtn">
          <div flex flex-items-center>
            <i class="iconfont icon-tianjia1" mr-2></i>
            <span>新增句子</span>
          </div>
        </a-button>
      </template>
      <!-- 右侧 -->
      <template #hideNumbers>
        <a-checkbox v-model:checked="config.quesProperties.hideNumbers">
          <p class="hide-numbers">隐藏题目序号</p>
        </a-checkbox>
      </template>
    </ToolBar>
  </div>
</template>

<script setup>
import ToolBar from '@/packages/global/ToolBar.vue';
import SpokenDragOptionBox from '@/packages/global/SpokenDragOptionBox.vue';
import RichText from '@/packages/global/RichText.vue';
import audioPlay from '@/packages/global/audioPlay.vue';
import SESConfigClass from './SESconfig.js';
const editorStore = useEditorStore();

// 底部左侧工具栏
const leftList = [
  { key: 'addSubQues', name: '新增句子' },
  // { key: '_addOption', name: '新增句子' },
  { key: '_audio' },
  { key: '_spokenAnswer' },
  { key: '_indicators' },
];

// 底部右侧工具栏
const rightList = [{ key: 'hideNumbers' }];

const optionFontStyles = computed(() => {
  const styleMap = editorStore.getExamSetting.optionFontStyles;
  return {
    ...styleMap,
    fontSize: styleMap.fontSize + 'px' || '18px',
  };
});

const props = defineProps({
  config: {
    type: Object,
    default: () => {},
  },
});

// 选项工具栏
const optionTool = [{ key: '_delete' }, { key: '_image' }];

// 英文句子（read_sentence）
// 内容可以用这用四个英文半角字符 . ! ? ； 进行分句。
// 文本前面和中间不要出现（ ） [ 这三个符号。
// 文本末尾不能出现 [ 这个字符 ，可以只有一个（ 或者 ），不可以出现多个（ 或者 ）。
// 可支持全角字符（一个全角字符占两个字节,引擎先转全角到半角），占整个content节点内容字节数的大小不得超过10%。
// 不支持字符占整个content节点内容字节数的大小不得超过10%，常见不支持的字符如：@ , # , $ , % , & , * , { , }。
// 每句单词数不能超过100个，每句字节数不能超过1024个字节（分句符号也算作一个字节）。
// 所有单词数不超过1000个。

// 这里要做输入英文句子的校验

/**
 * 校验英文句子的合法性
 * @param {string} value - 输入的值
 * @param {Object} record - 记录对象
 */
const textBlur = (_, value, record) => {
  // 清除HTML标签，获取纯文本内容
  const plainText = value.replace(/<[^>]*>/g, '').trim();
  record.textTitle = plainText.replace(/&nbsp;/g, ''); // 直接删除;
  // 如果内容为空，不进行校验
  if (!plainText) {
    record.errorMessage = '';
    return;
  }

  const validRegex = /^[a-zA-Z\s.!?&;,'-]*$/;

  if (!validRegex.test(plainText)) {
    record.errorMessage = "只允许输入英文字母和特定符号（. - ' , ! ? ;)";
    return;
  }

  // 2. 检查文本前面和中间是否出现了 ( ) [ 这三个符号
  // const frontAndMiddleText = plainText.slice(0, -1); // 除了最后一个字符
  // if (/[\(\)\[]/.test(frontAndMiddleText)) {
  //   record.errorMessage = '文本前面和中间不能出现 ( ) [ 这三个符号';
  //   return;
  // }

  // 3. 检查文本末尾是否出现了 [ 这个字符，以及 ( ) 符号的使用是否合法
  // const lastChar = plainText.slice(-1);
  // if (lastChar === '[') {
  //   record.errorMessage = '文本末尾不能出现 [ 这个字符';
  //   return;
  // }

  // 检查末尾的 ( ) 符号
  // const leftParenCount = (plainText.match(/\(/g) || []).length;
  // const rightParenCount = (plainText.match(/\)/g) || []).length;

  // if (leftParenCount > 1 || rightParenCount > 1) {
  //   record.errorMessage = '文本中不能出现多个 ( 或 ) 符号';
  //   return;
  // }

  // 4. 检查全角字符占比是否超过10%
  // const fullWidthChars = plainText.match(/[\uff01-\uff5e]/g) || [];
  // const fullWidthCharCount = fullWidthChars.length;
  // const totalBytes = new Blob([plainText]).size;

  // if (fullWidthCharCount * 2 > totalBytes * 0.1) {
  //   record.errorMessage = '全角字符占比不能超过10%';
  //   return;
  // }

  // 5. 检查不支持的字符占比是否超过10%
  // const unsupportedChars = plainText.match(/[@#$%&*{}]/g) || [];
  // const unsupportedCharCount = unsupportedChars.length;

  // if (unsupportedCharCount > 0) {
  //   if (unsupportedCharCount > totalBytes * 0.1) {
  //     record.errorMessage = `不支持的字符(如 ${Array.from(new Set(unsupportedChars)).join(' ')})占比不能超过10%`;
  //     return;
  //   }
  // }

  // 6. 检查每句单词数和字节数
  const sentences = plainText.split(/[.!?;]/).filter(s => s.trim().length > 0);

  for (let i = 0; i < sentences.length; i++) {
    const sentence = sentences[i];
    const words = sentence.trim().split(/\s+/);

    if (words.length > 100) {
      record.errorMessage = `第${i + 1}句单词数不能超过100个，当前有${words.length}个单词`;
      return;
    }

    const sentenceBytes = new Blob([sentence]).size + 1; // +1 是因为分句符号也算一个字节
    if (sentenceBytes > 1024) {
      record.errorMessage = `第${i + 1}句字节数不能超过1024个字节，当前有${sentenceBytes}个字节`;
      return;
    }
  }

  // 7. 检查所有单词数是否超过1000个
  const allWords = plainText.split(/\s+/);
  if (allWords.length > 1000) {
    record.errorMessage = `所有单词数不能超过1000个，当前有${allWords.length}个单词`;
    return;
  }

  // 校验通过，清除错误信息
  record.textTitle = plainText.replace(/&nbsp;/g, ''); // 直接删除

  record.errorMessage = '';
};

const addSubQuesBtn = () => {
  // 新增句子子题
  const subConfig = new SESConfigClass();
  props.config.subQues.push(subConfig);
};
</script>

<style lang="less" scoped>
.score-wrap {
  display: flex;
  align-items: center;
  .title {
    font-weight: 400;
    font-size: 14px;
    color: #8c8c8c;
    white-space: nowrap;
  }

  :deep(.ant-input) {
    width: 62px;
    padding: 0;
    height: 26px;
    background: #ffffff;
    border-radius: 4px;
    border: 1px solid #d9d9d9;
    font-weight: 400;
    font-size: 14px;
    color: #ffaa00;
    transition: none;
    text-align: center;
  }
}

.errorbox {
  padding-left: 20px;
  color: #f5222d;
  font-size: 12px;
  padding-top: 4px;
}
</style>
