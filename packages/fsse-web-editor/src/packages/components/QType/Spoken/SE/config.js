import cloneDeep from 'lodash/cloneDeep';
import {
  PublicConfigClass,
  PublicOptionClass,
} from '@/packages/public/publicConfig.js';
import { SEConfig } from './index.js';
import SESConfigClass from './SESconfig.js';
// const getOptions = () => {
//   return Array.from(
//     { length: 4 },
//     (_, index) => new PublicOptionClass({ optionContent: '' })
//   );
// };

// 句子中每一个对话都是一个小题,也就是说SE的题只有大题和小题 没有选项这种东西,新增句子也是新增1个小题,大题的答案解析和指标与所有小题共享
const getSubQues = () => {
  return Array.from({ length: 1 }, (_, index) => new SESConfigClass());
};

export default class Config extends PublicConfigClass {
  quesTypeCode = SEConfig.key;
  // 当前的config信息
  config = cloneDeep(SEConfig);
  // // 选项
  // options = getOptions();
  // 题目别名

  // 句子子题 SES 默认先初始化1个题 后面自己一个对话一个题的push
  subQues = getSubQues();

  quesTypeAlias = SEConfig.title;

  constructor(op = {}) {
    super();
    // 由实例化是才决定需要需改的内容
    Object.assign(this, op);
  }
}
