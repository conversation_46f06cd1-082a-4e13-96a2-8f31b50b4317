<template>
  <div class="PS">
    <!-- 请输入英语短文 -->
    <div class="quesContentBox">
      <a-textarea
        @blur="quesContentBlur"
        @focus="quesContentFocus"
        v-model:value="config.quesContent"
        placeholder="请输入短文"
        :auto-size="{ minRows: 8 }"
      />
      <div v-if="state.errorText" class="error_box_tip">
        {{ state.errorText }}
      </div>
    </div>
    <!-- 音频 -->
    <audioPlay :config="config" />
    <ToolBar :config="config" :leftList="leftList" :rightList="rightList">
    </ToolBar>
  </div>
</template>

<script setup>
import ToolBar from '@/packages/global/ToolBar.vue';
import audioPlay from '@/packages/global/audioPlay.vue';

import { message } from 'ant-design-vue';

const state = reactive({
  errorText: '',
});
// 底部左侧工具栏
const leftList = [
  { key: '_audio' },
  { key: '_spokenAnswer' },
  { key: '_indicators' },
];

// 底部右侧工具栏
const rightList = [];

const props = defineProps({
  config: {
    type: Object,
    default: () => {},
  },
});

// quesContent强制要求必须全满足下面的要求 否则就是不合法的输入
// quesContent示例：
// （1）必要节点：[content]，注意使用换行符进行分隔。
// （2）内容可以用这用四个英文半角字符 . ! ? ； 进行分句。
// （3）文本前面和中间不要出现（ ） [ 这三个符号。
// （4）文本末尾不能出现 [ 这个字符 ，可以只有一个（ 或者 ），不可以出现多个（ 或者 ）。
// （5）可支持全角字符（一个全角字符占两个字节,引擎先转全角到半角），占整个content节点内容字节数的大小不得超过10%。
// （6）不支持字符占整个content节点内容字节数的大小不得超过10%，常见不支持的字符如：@ , # , $ , % , & , * , { , }。
// （7）每句单词数不能超过100个，每句字节数不能超过1024个字节（分句符号也算作一个字节）。
// （8）所有单词数不超过1000个。
// （9）文本中不要添加无意义的字符组合，例如数字，字母与符号的各种组合，比如7FH34J。

/**
 * 校验讯飞语音评测试卷格式
 * @param {string} text 待校验的文本
 * @returns {object} 校验结果，包含是否通过和错误信息
 */
const validateContent = text => {
  if (!text || text === '题目内容') {
    return { valid: false, message: '请输入短文内容' };
  }

  // 直接使用用户输入的文本，不需要检查[content]节点
  const contentText = text;

  const validRegex = /^[a-zA-Z\s.!?;,'-]*$/;

  if (!validRegex.test(contentText)) {
    return {
      valid: false,
      message: "只允许输入英文字母和特定符号（. - ' , ! ? ;)",
    };
  }

  // 检查文本前面和中间的非法符号
  // const frontAndMiddleText = contentText.slice(0, -1); // 除了最后一个字符
  // if (/[\(\)\[]/.test(frontAndMiddleText)) {
  //   return { valid: false, message: '文本前面和中间不能出现（ ） [ 这三个符号' };
  // }

  // 检查4：文本末尾不能出现 [ 这个字符，可以只有一个（ 或者 ），不可以出现多个（ 或者 ）
  // const lastChar = contentText.slice(-1);
  // if (lastChar === '[') {
  //   return { valid: false, message: '文本末尾不能出现 [ 这个字符' };
  // }

  // const endParenthesesCount = (contentText.match(/\($/g) || []).length + (contentText.match(/\)$/g) || []).length;
  // if (endParenthesesCount > 1) {
  //   return { valid: false, message: '文本末尾不能出现多个（ 或者 ）' };
  // }

  // 检查2：使用四个英文半角字符 . ! ? ； 进行分句
  const sentences = contentText.split(/[.!?;]/);

  // 检查7：每句单词数不能超过100个，每句字节数不能超过1024个字节
  for (let i = 0; i < sentences.length; i++) {
    const sentence = sentences[i].trim();
    if (sentence === '') continue;

    // 计算单词数
    const words = sentence.split(/\s+/).filter(word => word.length > 0);
    if (words.length > 100) {
      return { valid: false, message: `第${i + 1}句单词数超过100个，请调整` };
    }

    // 计算字节数（包括分句符号）
    const byteLength = new Blob([
      sentence + (i < sentences.length - 1 ? '.' : ''),
    ]).size;
    if (byteLength > 1024) {
      return {
        valid: false,
        message: `第${i + 1}句字节数超过1024个字节，请调整`,
      };
    }
  }

  // 检查8：所有单词数不超过1000个
  const allWords = contentText.split(/\s+/).filter(word => word.length > 0);
  if (allWords.length > 1000) {
    return { valid: false, message: '所有单词数不能超过1000个' };
  }

  // 检查5和6：全角字符和不支持字符的比例
  // const totalBytes = new Blob([contentText]).size;

  // 检查全角字符
  // const fullWidthChars = contentText.match(/[\uff01-\uff5e\u3000-\u303f\u3300-\u33ff\ufe30-\ufe4f\uf900-\ufaff\u2000-\u206f\u2e00-\u2e7f]/g) || [];
  // const fullWidthBytes = new Blob([fullWidthChars.join('')]).size;
  // if (fullWidthBytes / totalBytes > 0.1) {
  //   return { valid: false, message: '全角字符占比不能超过10%' };
  // }

  // 检查不支持的字符
  // const unsupportedChars = contentText.match(/[@#$%&*{}]/g) || [];
  // const unsupportedBytes = new Blob([unsupportedChars.join('')]).size;
  // if (unsupportedBytes / totalBytes > 0.1) {
  //   return { valid: false, message: '不支持的字符（如@#$%&*{}）占比不能超过10%' };
  // }

  // 检查9：无意义的字符组合
  // const meaninglessPattern = /\d+[A-Za-z]+\d*|[A-Za-z]+\d+[A-Za-z]*/g;
  // const meaninglessMatches = contentText.match(meaninglessPattern) || [];
  // if (meaninglessMatches.length > 0) {
  //   return { valid: false, message: '文本中不要添加无意义的字符组合，如数字、字母与符号的组合（例如7FH34J）' };
  // }

  return { valid: true, message: '' };
};

const quesContentBlur = e => {
  console.log(e.target.value, '短文失焦了 要处理很多校验规则');
  if (e.target.value === '') {
    props.config.quesContent = '题目内容';
    return;
  }

  // 校验内容格式
  const validation = validateContent(e.target.value);
  if (!validation.valid) {
    state.errorText = validation.message;
  } else {
    state.errorText = '';
  }
};

const quesContentFocus = e => {
  console.log(e.target.value, '短文聚焦了');
  if (e.target.value === '题目内容') {
    props.config.quesContent = '';
    state.errorText = '';
  }
};
</script>

<style lang="less" scoped>
.error_box_tip {
  color: #f5222d;
  font-size: 12px;
  padding-top: 4px;
}
</style>
