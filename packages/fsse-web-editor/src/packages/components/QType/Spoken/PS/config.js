import cloneDeep from 'lodash/cloneDeep';
import {
  PublicConfigClass,
} from '@/packages/public/publicConfig.js';
import { PSConfig } from './index.js';

export default class Config extends PublicConfigClass {
  quesTypeCode = PSConfig.key;
  // 当前的config信息
  config = cloneDeep(PSConfig);

  // 题目别名
  quesTypeAlias = PSConfig.title;

  constructor(op = {}) {
    super();
    // 由实例化是才决定需要需改的内容
    Object.assign(this, op);
  }
}
