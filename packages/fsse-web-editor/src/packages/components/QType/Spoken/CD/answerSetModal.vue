<!-- 回答情景设置的modal -->

<template>
  <a-modal
    :maskClosable="false"
    v-model:open="state.open"
    title="回答情景设置"
    width="800px"
    @cancel="cancelCallback"
    @ok="confirm"
    :bodyStyle="{
      overflowY: 'auto',
      maxHeight: '660px',
      padding: '24px',
    }"
  >
    <div>
      <div class="dialogueTitle">对话{{ state.index + 1 }}</div>

      <div class="firstBox">
        <div class="tipBox">第一步：列举学生可能回答的所有情况</div>

        <table class="setTable">
          <thead>
            <tr class="headBox">
              <th class="indexBox thBox">序号</th>
              <th class="situation thBox">情景</th>
              <th class="operation thBox">{{ '' }}</th>
            </tr>
          </thead>
          <tbody>
            <tr
              class="bodyBox"
              v-for="(item, index) in state.choiceList"
              :key="item.id"
            >
              <td class="indexBox tdBox">{{ index + 1 }}</td>
              <td class="situation tdBox">
                <a-textarea
                  auto-size
                  v-model:value="item.name"
                  placeholder="请输入"
                />
              </td>
              <td class="operation tdBox">
                <PlusCircleFilled
                  @click="addChoice"
                  style="color: #00b781"
                  v-if="index === 0"
                />
                <MinusCircleFilled
                  @click="removeChoice(index)"
                  style="color: #f5222d"
                  v-else
                />
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <div class="secondBox">
        <div class="tipBox" pt24>
          第二步：请为以上情景设置关键词，即学生回答包含关键词则视为正确，不同关键词请使用
          | 间隔
        </div>

        <div class="keyTable">
          <div class="keyTitle">关键词</div>

          <div class="keyInput">
            <a-textarea
              v-model:value="state.keywords"
              placeholder="请输入"
              auto-size
            />
          </div>
        </div>
      </div>
    </div>
  </a-modal>
</template>

<script setup>
import { v4 as uuidv4 } from 'uuid';
const state = reactive({
  open: false,
  record: null,
  index: 0,
  keywords: '',
  choiceList: [
    {
      name: '',
      id: uuidv4(),
    },
  ],
});

const showModal = (record, index) => {
  console.log(record, index);
  state.record = record;

  state.index = index;

  if (record?.quesProperties?.keywords) {
    state.keywords = record?.quesProperties?.keywords;
  }
  if (
    record?.quesProperties?.choiceList &&
    record?.quesProperties?.choiceList?.length
  ) {
    state.choiceList = record?.quesProperties?.choiceList.map(item => {
      return {
        name: item,
        id: uuidv4(),
      };
    });
  }
  state.open = true;
};

const addChoice = () => {
  state.choiceList.push({
    name: '',
    id: uuidv4(),
  });
};

const removeChoice = index => {
  state.choiceList.splice(index, 1);
};

const cancelCallback = () => {
  state.choiceList = [
    {
      name: '',
      id: uuidv4(),
    },
  ];
  state.keywords = '';
  state.open = false;
};

const confirm = () => {
  const choice = state.choiceList.map(item => item.name);
  state.record.quesProperties.choiceList = choice;
  state.record.quesProperties.keywords = state.keywords;
  cancelCallback();
};

defineExpose({
  showModal,
});
</script>

<style scoped>
.tipBox {
  padding-bottom: 12px;
  font-weight: 400;
  font-size: 14px;
  color: #999999;
  line-height: 20px;
  text-align: left;
  font-style: normal;
}

.setTable {
  width: 100%;
  border-left: 1px solid #d9d9d9;
  border-collapse: collapse;
}

th.thBox {
  text-align: left;
  font-weight: normal;
  padding: 8px;
}

td.tdBox {
  padding: 12px;
}

.indexBox {
  border-top: 1px solid #d9d9d9;
  border-right: 1px solid #d9d9d9;
  width: 40px;
}

.situation {
  border-top: 1px solid #d9d9d9;
  border-right: 1px solid #d9d9d9;
  text-align: center !important;
}

.operation {
  border-top: 1px solid transparent;
  cursor: pointer;
  width: 40px;
}

.headBox {
  .indexBox {
    background-color: #f0f0f0;
  }
  .situation {
    background-color: #f0f0f0;
  }
  .operation {
    background-color: transparent;
  }
}

.bodyBox:last-child .indexBox,
.bodyBox:last-child .situation {
  border-bottom: 1px solid #d9d9d9;
}

.keyTable {
  width: 92%;
  .keyTitle {
    padding: 12px;
    background-color: #f0f0f0;
    border: 1px solid #d9d9d9;
  }
  .keyInput {
    padding: 12px;
    border-right: 1px solid #d9d9d9;
    border-left: 1px solid #d9d9d9;
    border-bottom: 1px solid #d9d9d9;
  }
}

.dialogueTitle {
  font-weight: 500;
  font-size: 14px;
  color: #262626;

  padding-bottom: 8px;
}
</style>
