import cloneDeep from 'lodash/cloneDeep';
import { PublicConfigClass } from '@/packages/public/publicConfig.js';
import { CDConfig } from './index.js';
import CDSConfigClass from './CDSconfig.js';


// 情景对话中每一个对话都是一个小题,也就是说cd的题只有大题和小题 没有选项这种东西,新增对话也是新增1个小题,大题的答案解析和指标与所有小题共享
const getSubQues = () => {
  return Array.from({ length: 1 }, (_, index) => new CDSConfigClass());
};

export default class Config extends PublicConfigClass {
  quesTypeCode = CDConfig.key;
  // 当前的config信息
  config = cloneDeep(CDConfig);

  // 没有选项
  options = [];

  // 题目别名
  quesTypeAlias = CDConfig.title;

  // 情景对话子题 CDS 默认先初始化1个题 后面自己一个对话一个题的push
  subQues = getSubQues();

  constructor(op = {}) {
    super();
    // 由实例化是才决定需要需改的内容
    Object.assign(this, op);
  }
}
