<template>
  <div class="CD">
    <!-- 情景对话输入题目内容的地方 有可能会包含图文 所以使用 YEditor  -->
    <div class="quesContentBox">
      <!-- <YEditor
        :style="quesContentFontStyles"
        v-model="config.quesContent"
        v-model:isDefault="config.quesProperties.defaultContent"
      ></YEditor> -->

      <a-textarea
        @blur="quesContentBlur"
        @focus="quesContentFocus"
        v-model:value="config.quesContent"
        placeholder="请输入"
        :auto-size="{ minRows: 4 }"
      />
      <div v-if="state.errorText" class="error_box_tip">
        {{ state.errorText }}
      </div>
    </div>
    <!-- 音频 -->
    <audioPlay :config="config" />
    <!-- 情景对话的对话区域 -->
    <CDSubQuesBox :config="config" :optionTool="optionTool">
      <template #top="{ record, index }">
        <div class="cdIndex">
          对话
          {{ index + 1 }}
        </div>
      </template>

      <!-- 这里输入的其实是小题的题干部分 -->
      <template #middle="{ record, index }">
        <RichText
          @textBlur="(e, value) => textBlur(e, value, record)"
          v-model="record.title"
        >
        </RichText>
      </template>

      <template #bottom="{ record, index }">
        <div v-if="record.errorMessage" class="error_box_text">
          {{ record.errorMessage }}
        </div>
        <div class="answerBox">
          <div class="answerTitle">answer：</div>
          <div class="disableBox"></div>
          <div class="answerSet" @click="answerSetModal(record, index)">
            回答情景设置
          </div>
        </div>
      </template>
    </CDSubQuesBox>

    <ToolBar :config="config" :leftList="leftList" :rightList="rightList">
      <template #addSubQues>
        <a-button type="link" class="btn-link-color" @click="addSubQuesBtn">
          <div flex flex-items-center>
            <i class="iconfont icon-tianjia1" mr-2></i>
            <span>新增对话</span>
          </div>
        </a-button>
      </template>
    </ToolBar>

    <AnswerSetModal ref="answerSetModalRef"></AnswerSetModal>
  </div>
</template>

<script setup>
import ToolBar from '@/packages/global/ToolBar.vue';
import RichText from '@/packages/global/RichText.vue';
import audioPlay from '@/packages/global/audioPlay.vue';
import CDSubQuesBox from './CDSubQuesBox.vue';
import AnswerSetModal from './answerSetModal.vue';
import CDSConfigClass from './CDSconfig.js';
const answerSetModalRef = ref(null);
const editorStore = useEditorStore();

const state = reactive({
  errorText: '',
});

const quesContentFontStyles = computed(() => {
  const styleMap = editorStore.getExamSetting.quesContentFontStyles;
  return {
    ...styleMap,
    fontSize: styleMap.fontSize + 'px' || '14px',
  };
});

// 底部左侧工具栏
const leftList = [
  { key: 'addSubQues' }, // 新增对话 子题
  // { key: '_addOption', name: '新增对话' },
  { key: '_audio' },
  { key: '_spokenAnswer' },
  { key: '_indicators' },
];

// 底部右侧工具栏
const rightList = [];
const optionTool = [];

const props = defineProps({
  config: {
    type: Object,
    default: () => {},
  },
});

const answerSetModal = (record, index) => {
  answerSetModalRef.value.showModal(record, index);
};

const addSubQuesBtn = () => {
  // 新增对话子题
  const subConfig = new CDSConfigClass();
  props.config.subQues.push(subConfig);
};

// quesContent强制要求必须全满足下面的要求 否则就是不合法的输入
// quesContent示例：
// （1）必要节点：[content]，注意使用换行符进行分隔。
// （2）内容可以用这用四个英文半角字符 . ! ? ； 进行分句。
// （3）文本前面和中间不要出现（ ） [ 这三个符号。
// （4）文本末尾不能出现 [ 这个字符 ，可以只有一个（ 或者 ），不可以出现多个（ 或者 ）。
// （5）可支持全角字符（一个全角字符占两个字节,引擎先转全角到半角），占整个content节点内容字节数的大小不得超过10%。
// （6）不支持字符占整个content节点内容字节数的大小不得超过10%，常见不支持的字符如：@ , # , $ , % , & , * , { , }。
// （7）每句单词数不能超过100个，每句字节数不能超过1024个字节（分句符号也算作一个字节）。
// （8）所有单词数不超过1000个。
// （9）文本中不要添加无意义的字符组合，例如数字，字母与符号的各种组合，比如7FH34J。

// 这里要做cd对话的Questions 的校验
const textBlur = (e, value, record) => {
  // 清除HTML标签，获取纯文本内容
  const plainText = value.replace(/<[^>]*>/g, '').trim();
  const onlyText = plainText.replace(/&nbsp;/g, ''); // 直接删除;
  record.textTitle = onlyText;
  console.log(record.textTitle, '23213');

  const validRegex = /^[a-zA-Z\s.!?;,'-]*$/;
  if (!validRegex.test(onlyText)) {
    record.errorMessage = "只允许输入英文字母和特定符号（. - ' , ! ? ;)";
  } else {
    record.errorMessage = '';
  }
};

const quesContentBlur = e => {
  if (e.target.value === '') {
    props.config.quesContent = '题目内容';
    return;
  }

  // 校验内容格式
  const validRegex = /^[a-zA-Z\s.!?;,'-]*$/;
  if (!validRegex.test(e.target.value)) {
    state.errorText = "只允许输入英文字母和特定符号（. - ' , ! ? ;)";
  } else {
    state.errorText = '';
  }
};

const quesContentFocus = e => {
  if (e.target.value === '题目内容') {
    props.config.quesContent = '';
    state.errorText = '';
  }
};
</script>

<style lang="less" scoped>
.cdIndex {
  font-weight: 500;
  font-size: 18px;
  color: rgba(0, 0, 0, 0.85);
  padding-bottom: 16px;
}

.quesContentBox {
  padding-bottom: 24px;
}
.answerBox {
  padding-top: 12px;
  padding-left: 24px;
  display: flex;
  align-items: center;

  .answerTitle {
    font-weight: 500;
    font-size: 18px;
    color: #333333;
  }
  .disableBox {
    height: 30px;
    background: #eeeeee;
    border: 1px solid #eeeeee;

    width: 51%;
  }
  .answerSet {
    padding-left: 16px;
    cursor: pointer;

    font-weight: 400;
    font-size: 14px;
    color: #00b781;
  }
}
.error_box_tip {
  color: #f5222d;
  font-size: 12px;
  padding-top: 4px;
}

.error_box_text {
  padding-left: 100px;
  color: #f5222d;
  font-size: 12px;
  padding-top: 4px;
}
</style>
